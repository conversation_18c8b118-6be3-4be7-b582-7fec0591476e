server {
  listen 80;

  # gzip
  gzip on;
  gzip_static on;
  gzip_min_length 1k;
  gzip_buffers 4 16k;
  gzip_http_version 1.1;
  gzip_comp_level 3;
  gzip_types text/plain application/javascript application/x-javascript text/javascript text/css application/xml application/xml+rss image/svg+xml application/json;
  gzip_vary on;
  gzip_proxied expired no-cache no-store private auth;
  gzip_disable "MSIE [1-6].";

  client_max_body_size 10m;
  # 配置代理超时时间（连接、读取、发送）
  proxy_connect_timeout 300s;
  proxy_read_timeout 300s;
  proxy_send_timeout 300s;

  proxy_set_header Host $host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

  # proxy_hide_header referer;

  location / {
    root /usr/share/nginx/html;
    index index.html index.htm;
    try_files $uri $uri/ @rewrites;
  }

  location @rewrites {
    rewrite ^(.+)$ /index.html last;
  }

  location /api_global_intelligence {
    rewrite ^/api_global_intelligence/(.*)$ /$1 break;
    proxy_pass http://************:31233;
  }

  error_page 500 502 503 504 /50x.html;
  location = /50x.html {
    root /usr/share/nginx/html;
  }
}
