image: node:16

variables:
  DOCKER_INDEX: 'index.docker.datatub.com'
  PRODUCT_DOCKER_INDEX: 'prd-harbor.datastory.com.cn'
  DOCKER_REGISTRY: 'prd-harbor.datastory.com.cn'
  WECHAT_BOT_TOKEN: '94dd6a15-fa29-42fb-9bad-9fe055855363'
  WECHAT_BOT_MENTION: '["13229677963"]'

cache:
  key: cache-all-across-jobs
  paths:
    - dist/

stages:
  # - dev-build
  - dev-docker-build
  - dev-production
  # - prd-build
  - prd-docker-build
  - prd-production
  - notify-image

# dev-build:
#   stage: dev-build
#   image: node:16
#   tags:
#     - deploy:fs-ops-vm-cicd1
#   script:
#     - node -v
#     - npm install --registry=https://registry.npmmirror.com
#     - npm run build:staging
#     - rm -f dist/**/js/*.map
#   only:
#     - develop

dev-docker-build:
  stage: dev-docker-build
  tags:
    - deploy:fs-ops-vm-cicd1
  script:
    - DOCKER_IMAGE_TAG=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}:release.${CI_COMMIT_SHORT_SHA}
    - echo ${DOCKER_IMAGE_TAG}
    - docker build --build-arg STAGE=dev -f Dockerfile -t ${DOCKER_IMAGE_TAG} .
    - docker tag ${DOCKER_IMAGE_TAG} ${PRODUCT_DOCKER_INDEX}/${DOCKER_IMAGE_TAG}
    - docker push ${PRODUCT_DOCKER_INDEX}/${DOCKER_IMAGE_TAG}
  only:
    - develop

dev-production:
  stage: dev-production
  tags:
    - deploy:fs-ops-vm-cicd1
  #  when: manual
  script:
    - export IMAGE_TAG=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}:release.${CI_COMMIT_SHORT_SHA}
    - export DOCKER_REGISTRY
    - export NAMESPACE=b2b-uat
    - export BUILD_TIME=$(date)
    - export APP_NAME=dt-b2b-static-global-intelligence-web
    #TODO:用npm获取当前项目版本
    - export VERSION=${CI_COMMIT_SHORT_SHA}
    - cat kubernetes.yml|envsubst
    - echo ${IMAGE_TAG}
    - envsubst < kubernetes.yml | kubectl --kubeconfig ${DEV_KUBE_CONFIG_V3} apply -f  -
  only:
    - develop

# prd-build:
#   stage: prd-build
#   tags:
#     - deploy:fs-ops-vm-cicd1
#   script:
#     - node -v
#     - npm install --registry=https://registry.npmmirror.com
#     - npm run build:production
#     - rm -f dist/**/js/*.map
#   only:
#     - main

prd-docker-build:
  stage: prd-docker-build
  tags:
    - deploy:fs-ops-vm-cicd1
  script:
    - DOCKER_IMAGE_TAG=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}:release.${CI_COMMIT_SHORT_SHA}
    - echo ${DOCKER_IMAGE_TAG}
    - docker build --build-arg STAGE=prod -f Dockerfile -t ${DOCKER_IMAGE_TAG} .
    - docker tag ${DOCKER_IMAGE_TAG} ${PRODUCT_DOCKER_INDEX}/${DOCKER_IMAGE_TAG}
    - docker push ${PRODUCT_DOCKER_INDEX}/${DOCKER_IMAGE_TAG}
  only:
    - main

prd-production:
  stage: prd-production
  tags:
    - deploy:fs-ops-vm-cicd1
  #  when: manual
  script:
    - export IMAGE_TAG=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}:release.${CI_COMMIT_SHORT_SHA}
    - export DOCKER_REGISTRY
    - export NAMESPACE=b2b
    - export BUILD_TIME=$(date)
    - export APP_NAME=dt-b2b-static-global-intelligence-web
    #TODO:用npm获取当前项目版本
    - export VERSION=${CI_COMMIT_SHORT_SHA}
    - cat kubernetes.yml|envsubst
    - echo ${IMAGE_TAG}
    - envsubst < kubernetes.yml | kubectl --kubeconfig ${PRD_KUBE_CONFIG_V3} apply -f  -
  only:
    - main

# 构建失败时的通知消息
notifyFailWeChat:
  only:
    - main
    - develop
  stage: notify-image
  script:
    - curl "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=$WECHAT_BOT_TOKEN" -H 'Content-Type:application/json' -d "{\"msgtype\":\"markdown\",\"markdown\":{\"content\":\"$CI_PROJECT_NAME项目构建结果：<font color='warning'>失败</font>\n>本次构建由：$GITLAB_USER_NAME 触发\n>构建分支：$CI_COMMIT_BRANCH\n>提交日志：$CI_COMMIT_MESSAGE>流水线地址：[$CI_PIPELINE_URL]($CI_PIPELINE_URL)\n>其他信息：<font color='info'>image-构建失败</font>\",\"mentioned_mobile_list\":$WECHAT_BOT_MENTION}}"
  tags:
    - deploy:fs-ops-vm-cicd1
  when: on_failure
  except:
    variables:
      - $CI_COMMIT_MESSAGE =~ /maven-release-plugin/
      - $CI_COMMIT_MESSAGE =~ / \#skipcicd/

# 构建成功时的通知消息
notifySuccessWeChat:
  only:
    - main
  stage: notify-image
  script:
    - curl "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=$WECHAT_BOT_TOKEN" -H 'Content-Type:application/json' -d "{\"msgtype\":\"markdown\",\"markdown\":{\"content\":\"$CI_PROJECT_NAME项目构建结果：<font color='info'>成功</font>\n>本次构建由：$GITLAB_USER_NAME 触发\n>构建分支：$CI_COMMIT_BRANCH\n>提交日志：$CI_COMMIT_MESSAGE>流水线地址：[$CI_PIPELINE_URL]($CI_PIPELINE_URL)\n>其他信息：<font color='info'>image-构建成功</font>\",\"mentioned_mobile_list\":$WECHAT_BOT_MENTION}}"
  tags:
    - deploy:fs-ops-vm-cicd1
  when: on_success
  except:
    variables:
      - $CI_COMMIT_MESSAGE =~ /maven-release-plugin/
      - $CI_COMMIT_MESSAGE =~ / \#skipcicd/
