<template>
  <div class="h100% min-h1200px flex flex-column">
    <div class="titleBox p-16px flex items-center">
      <p class="fw-550 color-#000-88 fs-20 mr-8px">行业看板</p>
      <a-select
        v-model:value="currentIndustry"
        :options="industryList"
        :fieldNames="{ label: 'labelName', value: 'id' }"
        size="small"
        style="width: 140px"
        @change="handleIndustryChange"
      ></a-select>
    </div>

    <div class="bg-#f7f7f7 flex-1 p16px overflow-hidden">
      <a-row :gutter="16" class="h60%">
        <a-col :span="16" class="h100%">
          <worldMap ref="worldMapRef" :industryId="currentIndustry" @click="openDetailModal" />
        </a-col>
        <a-col :span="8" class="h100%">
          <a-card title="政府公告" :bodyStyle="{ padding: 0 }">
            <a-list :loading="noticeLoading" item-layout="vertical" :data-source="noticeDataList">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta
                    :description="`${item.websiteName} ${dayjs(item.publishTime).format('YYYY-M-D')}`"
                    :style="{ marginBlockEnd: 0 }"
                  >
                    <template #title>
                      <a @click="openDetailModal(item)">{{ item.title }}</a>
                    </template>
                    <template #avatar>
                      <div class="flex-inherit-start">
                        <img :src="getAssetsFile(countryMap[item.country])" class="w30px h20px" />
                      </div>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
              <template #loadMore>
                <div
                  v-if="noticeDataList.length > 0"
                  class="loadMore py-8px"
                  v-intersection-observer="handlerNoticeIntersectionObserver"
                >
                  <p class="endText" v-if="!noticeNoMore"><a-spin tip="加载中..."></a-spin></p>
                  <p class="endText" v-else>没有更多了</p>
                </div>
              </template>
            </a-list>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" class="h40% pt16px">
        <a-col :span="8" class="h100%">
          <a-card title="近30日行业热度" :bodyStyle="{ flex: 1 }">
            <a-spin :spinning="industryHeatLoading">
              <vChart
                ref="vChartRef"
                class="h-100%"
                :option="industryHeatChartOptions"
                :init-options="{ locale: 'ZH' }"
                :autoresize="false"
              />
            </a-spin>
            <template #extra></template>
          </a-card>
        </a-col>

        <a-col :span="8" class="h100%">
          <a-card title="竞对动向" :bodyStyle="{ padding: 0 }">
            <a-list :loading="competitiveLoading" item-layout="vertical" :data-source="competitiveDataList">
              <template #renderItem="{ item }">
                <div class="flex items-start ant-list-item">
                  <div class="">
                    <a-tag color="#6553ee" class="w74px text-center ellipsis-1 m-0">{{ first(item.brandList) }}</a-tag>
                  </div>
                  <p class="flex-1 px-8px hoverPrimaryColor ellipsis-1" @click="openDetailModal(item)">
                    {{ item.title }}
                  </p>
                  <span class="color-#000-45">{{ dayjs(item.publishTime).format('YYYY-M-D') }}</span>
                </div>
              </template>
              <template #loadMore>
                <div
                  v-if="competitiveDataList.length > 0"
                  class="loadMore py-8px"
                  v-intersection-observer="handlerCompetitiveIntersectionObserver"
                >
                  <p class="endText" v-if="!competitiveNoMore"><a-spin tip="加载中..."></a-spin></p>
                  <p class="endText" v-else>没有更多了</p>
                </div>
              </template>
            </a-list>
          </a-card>
        </a-col>

        <a-col :span="8" class="h100% flex flex-column">
          <a-card title="外汇" class="mb-16px" style="height: fit-content">
            <a-spin :spinning="false" foreignExchangeLoading>
              <a-row :gutter="8">
                <a-col :span="8" v-for="(item, index) in foreignExchangeDataList" :key="index">
                  <div class="bg-f0eefd py-16px px-8px">
                    <div class="flex-center-center mb-8px">
                      <p class="fs-18px fw-600">{{ item?.medianPrice || '' }}</p>

                      <span
                        :class="[
                          Number(item?.upDown) > 0 ? 'color-#f5222d' : Number(item?.upDown) < 0 ? 'color-#52c41a' : '',
                          ' flex items-center fs-14'
                        ]"
                      >
                        <iconfontIcon
                          v-if="Number(item?.upDown) > 0"
                          icon="icon-caret-up-small"
                          class="fs-18px!"
                        ></iconfontIcon>
                        <iconfontIcon
                          v-if="Number(item?.upDown) < 0"
                          icon="icon-caret-down-small"
                          class="fs-18px!"
                        ></iconfontIcon>
                        {{ item?.upDown }}
                      </span>
                    </div>

                    <div class="flex-center-center">
                      <a-space class="fs-12px">
                        <img :src="countryIconMap[item.currencyPair as string]" class="h16px" />
                        <span>{{ item?.currencyPair }}</span>
                      </a-space>
                    </div>
                  </div>
                </a-col>
              </a-row>
            </a-spin>
          </a-card>

          <a-card title="商品价格" style="flex: 1" :bodyStyle="{ height: '100%', padding: '8px 16px' }">
            <a-spin :spinning="commodityPriceLoading">
              <div class="flex-wrap flex h100%">
                <div
                  v-for="(item, index) in commodityPriceDataList"
                  :key="index"
                  :class="['w50% flex-inherit-center', index % 2 == 0 ? 'pr20px' : 'pl20px']"
                >
                  <div>
                    <p class="fs-14px">{{ item.name }}</p>
                    <span class="fx-12px color-#000-45">{{ item.code }}</span>
                  </div>
                  <div class="flex-1 text-right">
                    <p class="fs-14px">{{ item.lastPrice }}</p>
                    <span class="fx-12px color-#000-45">{{ item.unit }}</span>
                  </div>
                  <div>
                    <span
                      :class="[
                        Number(item.riseFallRate.replace('%', '')) > 0
                          ? 'color-#f5222d'
                          : Number(item.riseFallRate.replace('%', '')) < 0
                          ? 'color-#52c41a'
                          : '',
                        'flex-center-center'
                      ]"
                    >
                      <iconfontIcon
                        v-if="Number(item.riseFallRate.replace('%', '')) > 0"
                        icon="icon-caret-up-small"
                      ></iconfontIcon>
                      <iconfontIcon
                        v-else-if="Number(item.riseFallRate.replace('%', '')) < 0"
                        icon="icon-caret-down-small"
                      ></iconfontIcon>
                      {{
                        item.riseFallRate !== '-' ? `${item.riseFallRate.replace('%', '')}%` : `${item.riseFallRate}`
                      }}
                    </span>
                  </div>
                </div>
              </div>
            </a-spin>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-modal
      v-model:open="visible"
      :footer="null"
      width="80%"
      @cancel="handleModalClose"
      wrapClassName="detailModal"
      :getContainer="false"
      :closable="false"
    >
      <a-spin :spinning="detailLoading" class="block mx-auto">
        <IntelligenceDetail
          :intelligenceInfo="detailData"
          :key="detailData.dataId"
          isShowCloseBtn
          @close="handleModalClose"
        />
      </a-spin>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import {
  getNewsDetail,
  getSystemLabel,
  informationBoardCommodityPrice,
  informationBoardCompetitiveTrends,
  informationBoardForeignExchange,
  informationBoardGovernmentNotice,
  informationBoardIndustryHost
} from '@/api/api'
import useInfiniteLoading from '@/hooks/useInfiniteLoading'
import useRequest from '@/hooks/useRequest'
import { computed, nextTick, onMounted, onUnmounted, ref, useTemplateRef, watch, type Ref } from 'vue'
import { vIntersectionObserver } from '@vueuse/components'
import dayjs from 'dayjs'
import { first } from 'lodash-es'
import iconfontIcon from '@/components/tools/iconfontIcon'
import VChart from 'vue-echarts'
import { use, type ComposeOption } from 'echarts/core'
import { BarChart, type BarSeriesOption } from 'echarts/charts'
import {
  TooltipComponent,
  GridComponent,
  type GridComponentOption,
  type TooltipComponentOption
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import { useWindowSize } from '@vueuse/core'
import type { competitiveTrendsResType } from '~/types/api/informationBoard/competitiveTrends'
import type { governmentNoticeResType } from '~/types/api/informationBoard/governmentNotice'
import IntelligenceDetail from '../intelligence/components/intelligenceDetail.vue'
import type { newsPageResType } from '~/types/api/news/page'
import countryMap from './config/countryMap.json'
import worldMap from './worldMap.vue'
import { CloseCircleOutlined } from '@ant-design/icons-vue'

type EChartsOption = ComposeOption<TooltipComponentOption | GridComponentOption | BarSeriesOption>
use([TooltipComponent, GridComponent, BarChart, CanvasRenderer])

const worldMapRef = useTemplateRef('worldMapRef')

const { dataList: labelMap, getData: getIndustry } = useRequest(
  getSystemLabel,
  { sceneType: 'news' },
  { immediateReqData: false }
)
const industryList = computed(() => labelMap.value?.industry)
const currentIndustry = ref('') // 当前选中的行业

/**
 * @description: 政府公告
 * @param {*} industryId
 * @return {*}
 */
function useNotice(industryId: Ref<string>) {
  const params = computed(() => ({ industryId: industryId.value }))
  const { dataList, loading, onLoadMore, noMore, refresh } = useInfiniteLoading(
    informationBoardGovernmentNotice,
    params,
    { immediateReqData: false }
  )

  /**
   * @description: 滚动到界面底部回调方法
   * @param {*} intersectionObserverList
   * @return {*}
   */
  function handlerIntersectionObserver(intersectionObserverList: IntersectionObserverEntry[]) {
    const { isIntersecting } = intersectionObserverList[0]
    if (isIntersecting && !noticeLoading.value && !noticeNoMore.value) {
      onLoadMore()
    }
  }

  return {
    noticeDataList: dataList,
    noticeLoading: loading,
    noticeNoMore: noMore,
    noticeRefresh: refresh,
    handlerNoticeIntersectionObserver: handlerIntersectionObserver
  }
}

// 近30日行业热度
const vChartRef = useTemplateRef<InstanceType<typeof VChart>>('vChartRef')

const { width, height } = useWindowSize()
watch([width, height], () => {
  vChartRef.value?.resize()
})

function useIndustryHeat(industryId: Ref<string>) {
  const params = computed(() => ({
    industryId: industryId.value
  }))
  const { getData, dataList, loading } = useRequest(informationBoardIndustryHost, params, { immediateReqData: false })

  const chartOptions = computed<EChartsOption>(() => {
    const xData = dataList.value?.map(item => item.country)
    const yData = dataList.value?.map(item => item.num)
    const options: EChartsOption = {
      tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
      grid: { containLabel: true, left: 'left', right: 0, top: 10, bottom: 0 },
      xAxis: {
        type: 'category',
        data: xData,
        axisLabel: {
          interval: 0
        }
      },
      yAxis: { type: 'value' },
      series: [{ data: yData, type: 'bar', color: '#6553ee' }]
    }

    return options
  })

  return {
    industryHeatGetData: getData,
    industryHeatDataList: dataList,
    industryHeatLoading: loading,
    industryHeatChartOptions: chartOptions
  }
}
// 竞对动向
function useCompetitive(industryId: Ref<string>) {
  const params = computed(() => ({ industryId: industryId.value }))
  const { dataList, loading, onLoadMore, noMore, refresh } = useInfiniteLoading(
    informationBoardCompetitiveTrends,
    params,
    { immediateReqData: false }
  )

  /**
   * @description: 滚动到界面底部回调方法
   * @param {*} intersectionObserverList
   * @return {*}
   */
  function handlerIntersectionObserver(intersectionObserverList: IntersectionObserverEntry[]) {
    const { isIntersecting } = intersectionObserverList[0]
    if (isIntersecting && !noticeLoading.value && !noticeNoMore.value) {
      onLoadMore()
    }
  }

  return {
    competitiveDataList: dataList,
    competitiveLoading: loading,
    competitiveNoMore: noMore,
    competitiveRefresh: refresh,
    handlerCompetitiveIntersectionObserver: handlerIntersectionObserver
  }
}
// 外汇
const { dataList: foreignExchangeDataList, loading: foreignExchangeLoading } = useRequest(
  informationBoardForeignExchange
)

const countryIconMap: Record<string, string> = {
  '美元/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/USD.jpg', // 'USD/CNY'
  '欧元/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/EUR.jpg', // 'EUR/CNY'
  '100日元/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/JPY.jpg', // '100JPY/CNY'
  '港元/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/HKD.jpg', // 'HKD/CNY'
  '英镑/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/GBP.jpg', // 'GBP/CNY'
  '澳元/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/AUD.jpg', // 'AUD/CNY'
  '新西兰元/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/NZD.jpg', // 'NZD/CNY'
  '新加坡元/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/SGD.jpg', // 'SGD/CNY'
  '瑞士法郎/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/CHF.jpg', // 'CHF/CNY'
  '加元/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/CAD.jpg', // 'CAD/CNY'
  '人民币/澳门元': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/MOP.jpg', // 'CNY/MOP'
  '人民币/马来西亚林吉特': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/MYR.jpg', // 'CNY/MYR'
  '人民币/俄罗斯卢布': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/RUB.jpg', // 'CNY/RUB'
  '人民币/南非兰特': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/ZAR.jpg', // 'CNY/ZAR'
  '人民币/韩元': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/KRW.jpg', // 'CNY/KRW'
  '人民币/阿联酋迪拉姆': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/AED.jpg', // 'CNY/AED'
  '人民币/沙特里亚尔': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/SAR.jpg', // 'CNY/SAR'
  '人民币/匈牙利福林': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/HUF.jpg', // 'CNY/HUF'
  '人民币/波兰兹罗提': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/PLN.jpg', // 'CNY/PLN'
  '人民币/丹麦克朗': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/DKK.jpg', // 'CNY/DKK'
  '人民币/瑞典克朗': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/SEK.jpg', // 'CNY/SEK'
  '人民币/挪威克朗': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/NOK.jpg', // 'CNY/NOK'
  '人民币/土耳其里拉': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/TRY.jpg', // 'CNY/TRY'
  '人民币/墨西哥比索': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/MXN.jpg', // 'CNY/MXN'
  '人民币/泰铢': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/THB.jpg' // 'CNY/THB'
}

// 商品价格
const {
  dataList: commodityPriceDataList,
  loading: commodityPriceLoading,
  getData: getCommodityPrice
} = useRequest(informationBoardCommodityPrice)

const { noticeDataList, noticeLoading, noticeNoMore, noticeRefresh, handlerNoticeIntersectionObserver } =
  useNotice(currentIndustry)

const { industryHeatGetData, industryHeatLoading, industryHeatChartOptions } = useIndustryHeat(currentIndustry)
const {
  competitiveDataList,
  competitiveLoading,
  competitiveNoMore,
  competitiveRefresh,
  handlerCompetitiveIntersectionObserver
} = useCompetitive(currentIndustry)

async function handleIndustryChange() {
  await nextTick()
  Promise.all([noticeRefresh(), industryHeatGetData(), competitiveRefresh(), worldMapRef.value?.getDynamicsData()])
}

const visible = ref(false)
const detailLoading = ref(false)
const detailData = ref<newsPageResType>({
  id: 0,
  dataId: '',
  title: '',
  translateTitle: '',
  summary: '',
  translateSummary: '',
  content: '',
  translateContent: '',
  author: '',
  detailUrl: '',
  host: '',
  publishTime: '',
  tagMap: [] as unknown as Record<string, string[]>,
  isRead: false,
  tagList: [],
  isZh: false,
  websiteName: ''
})
async function openDetailModal(item: governmentNoticeResType | competitiveTrendsResType) {
  console.log('item: ', item)
  try {
    visible.value = true
    detailLoading.value = true
    detailData.value.translateTitle = item.title
    detailData.value.dataId = item.dataId
    const { result } = await getNewsDetail({ dataId: item.dataId })
    detailData.value = result
    detailLoading.value = false
  } catch (error) {
    detailLoading.value = false
    visible.value = false
    console.error(error)
  }
}
function handleModalClose() {
  visible.value = false
  setTimeout(() => {
    detailData.value = {
      id: 0,
      dataId: '',
      title: '',
      translateTitle: '',
      summary: '',
      translateSummary: '',
      content: '',
      translateContent: '',
      author: '',
      detailUrl: '',
      host: '',
      publishTime: '',
      tagMap: [] as unknown as Record<string, string[]>,
      isRead: false,
      tagList: [],
      isZh: false,
      websiteName: ''
    }
  }, 500)
}

// 商品价格1分钟自动刷新。其它的5分钟刷新。
let intervalId1: number, intervalId2: number

onMounted(async () => {
  await getIndustry()
  if (industryList.value) {
    currentIndustry.value = industryList.value[0].id
    handleIndustryChange()
  }

  await nextTick()
  clearInterval(intervalId1)
  clearInterval(intervalId2)
  intervalId1 = setInterval(() => getCommodityPrice(), 1000 * 60 * 1)
  intervalId2 = setInterval(() => handleIndustryChange(), 1000 * 60 * 5)
})

onUnmounted(() => {
  clearInterval(intervalId1)
  clearInterval(intervalId2)
})

function getAssetsFile(path: string) {
  const imgPath = new URL(`../../assets/country/${path}`, import.meta.url).href
  if (!imgPath.includes('undefined')) {
    return imgPath
  }
  return ''
}
</script>

<style lang="less" scoped>
::v-deep(.ant-card) {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .ant-card-head {
    border: none;
    min-height: auto;
    padding: 16px 16px 0px;
  }
  .ant-card-body {
    overflow: auto;
  }

  .ant-spin-nested-loading,
  .ant-spin-container {
    height: 100%;
  }
}

::v-deep(.detailModal) {
  .ant-modal-content {
    padding: 16px 0;
  }
}
</style>
