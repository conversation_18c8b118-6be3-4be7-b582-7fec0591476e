<template>
  <div class="h100% flex flex-column">
    <!-- min-h1200px  -->
    <div class="titleBox p-16px flex items-center">
      <p class="fw-550 color-#000-88 fs-20 mr-8px">风险看板</p>
    </div>

    <div class="bg-#f7f7f7 flex-1 p16px">
      <a-row :gutter="[16, 16]">
        <a-col :span="14" :xxl="{ span: 16 }">
          <a-card :bodyStyle="{ height: '100%' }">
            <a-spin :spinning="effectiveRiskLoading">
              <div class="h100% overflow-hidden">
                <vChart
                  ref="vChartRef"
                  class="h100%"
                  :option="effectiveRiskChartOptions"
                  :init-options="{ locale: 'ZH' }"
                  :autoresize="true"
                  :theme="chartTheme"
                />
              </div>
            </a-spin>
          </a-card>
        </a-col>
        <a-col :span="10" :xxl="{ span: 8 }">
          <a-card :bodyStyle="{ height: `${line1Height}px` }">
            <template #title>
              <div class="flex items-center">
                <p class="flex-1">对我方影响</p>
                <a-space class="ml16px">
                  <span
                    v-for="(item, index) in [
                      { label: '全部', value: '' },
                      { label: '市场', value: '市场' },
                      { label: '供应链', value: '供应链' },
                      { label: '运输', value: '运输' },
                      { label: '办事处', value: '办事处' }
                    ]"
                    :key="index"
                    @click="changeImpact(item.value)"
                    :class="[
                      'fs-14px fw400 hoverPrimaryColor',
                      riskImpactParams.impactType === item.value ? 'color-#6553ee' : 'color-#333'
                    ]"
                    >{{ item.label }}</span
                  >
                  <!-- <a-tabs v-model:activeKey="riskImpactParams.impactType" @change="getRiskImpactData">
                    <a-tab-pane tab="" key=""></a-tab-pane>
                    <a-tab-pane tab="" key="市场"></a-tab-pane>
                    <a-tab-pane tab="" key="供应链"></a-tab-pane>
                    <a-tab-pane tab="" key="运输"></a-tab-pane>
                    <a-tab-pane tab="" key="办事处"></a-tab-pane>
                    <template #addIcon></template>
                  </a-tabs> -->
                </a-space>
              </div>
            </template>

            <a-table
              :columns="riskImpactColumns"
              :data-source="riskImpactDataList"
              :pagination="riskImpactPageParams"
              :loading="riskImpactLoading"
              size="small"
              :scroll="{ x: false, y: table1Height }"
            >
              <template #bodyCell="{ text, column }">
                <template v-if="column.dataIndex === 'level'">
                  <span v-if="text === 1" class="color-#D92400">关键</span>
                  <span v-else-if="text === 2" class="color-#fe6b00">重要</span>
                  <span v-else-if="text === 3" class="color-#faad14">有限</span>
                </template>
              </template>
            </a-table>
          </a-card>
        </a-col>
        <a-col :span="14" :xxl="{ span: 16 }">
          <a-card title="风险信息" :bodyStyle="{ height: `${line2Height}px` }">
            <div class="h100% flex flex-direction-column">
              <div class="flex-1">
                <a-table
                  :columns="riskInfoColumns"
                  :data-source="riskInfoDataList"
                  :pagination="false"
                  :loading="riskInfoLoading"
                  size="small"
                  :scroll="{ x: false, y: table2Height }"
                >
                  <template #bodyCell="{ text, column, record }">
                    <template v-if="column.dataIndex === 'riskSubType'"> {{ record.riskType }}-{{ text }} </template>
                    <template v-if="column.dataIndex === 'status'">
                      <template v-if="text === 1">恶化</template>
                      <template v-else-if="text === 2">不变</template>
                      <template v-else-if="text === 3">改善</template>
                      <template v-else-if="text === 4">
                        <a-tag class="m0" color="#eaeaea" style="color: #333">结束</a-tag>
                      </template>
                    </template>
                    <template v-if="column.dataIndex === 'impactLevel'">
                      <template v-if="text === 1">关键</template>
                      <template v-else-if="text === 2">重要</template>
                      <template v-else-if="text === 3">有限</template>
                    </template>
                    <template v-if="column.dataIndex === 'level'">
                      <span v-if="text === 1" class="color-#D92400">高</span>
                      <span v-else-if="text === 2" class="color-#fe6b00">中</span>
                      <span v-else-if="text === 3" class="color-#faad14">低</span>
                    </template>
                    <template v-if="column.dataIndex === 'alarmBeginTime'">
                      {{ text || '-' }}
                    </template>
                  </template>
                </a-table>
              </div>

              <div class="text-right">
                <a-pagination v-bind="riskInfoPageParams" size="small"></a-pagination>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="10" :xxl="{ span: 8 }">
          <a-card title="近1年风险类型分布" :bodyStyle="{ height: `${line2Height}px` }">
            <a-spin :spinning="top10Loading">
              <div class="h100% overflow-hidden">
                <vChart
                  ref="vChartRef"
                  :option="top10ChartOptions"
                  :init-options="{ locale: 'ZH' }"
                  :autoresize="true"
                  :theme="chartTheme"
                />
              </div>
            </a-spin>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { riskBoardEffectiveRisk, riskBoardRiskImpact, riskBoardRiskInfo, riskBoardRiskSubTypeTop } from '@/api/api'
import useListLoading from '@/hooks/useListLoading'
import useRequest from '@/hooks/useRequest'
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import VChart from 'vue-echarts'
import { use, registerMap } from 'echarts/core'
import { PieChart, ScatterChart } from 'echarts/charts'
import { TooltipComponent, GridComponent, GeoComponent, LegendComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import type { ComposeOption } from 'echarts/core'
import type { PieSeriesOption, ScatterSeriesOption } from 'echarts/charts'
import type { TooltipComponentOption, GridComponentOption, GeoComponentOption } from 'echarts/components'
import worldJson from '@/assets/worldJson.json'
import conflict1 from '@/assets/conflict1.svg'
import conflict2 from '@/assets/conflict2.svg'
import conflict3 from '@/assets/conflict3.svg'
import naturalDisaster1 from '@/assets/naturalDisaster1.svg'
import naturalDisaster2 from '@/assets/naturalDisaster2.svg'
import naturalDisaster3 from '@/assets/naturalDisaster3.svg'
import chartTheme from '@/assets/chartTheme.json'
import { useWindowSize } from '@vueuse/core'
import { floor } from 'lodash-es'

use([
  TooltipComponent,
  GeoComponent,
  LegendComponent,
  ScatterChart,
  CanvasRenderer,
  GridComponent,
  PieChart,
  CanvasRenderer
])
registerMap('world', worldJson)

// 卡片高度
const line1Height = ref(500)
const line2Height = ref(500)
const table1Height = ref(300)
const table2Height = ref(300)
const { height } = useWindowSize()
function setHeight() {
  nextTick(() => {
    const headerHeight = document.querySelector('.titleBox')?.clientHeight || 0
    const containerHeight = height.value - headerHeight - 32 - 16 // 32为上下边距，16为间隔
    // 设置卡片高度
    line1Height.value = floor(containerHeight * 0.5) - 45
    line2Height.value = floor(containerHeight * 0.5) - 45
    // 设置表格高度 卡片内容高度-间隔-表头-表格底部分页器
    table1Height.value = line1Height.value - 32 - 40 - 40
    table2Height.value = line2Height.value - 32 - 40 - 40
  })
}

watch([height], () => setHeight())

onMounted(async () => {
  await Promise.all([getRiskImpactData(), riskInfoGetData()])
  setHeight()
})

// 风险信息
const { loading: effectiveRiskLoading, dataList: effectiveRiskDataList } = useRequest(riskBoardEffectiveRisk)
type effectiveRiskChartOptionsType = ComposeOption<TooltipComponentOption | GeoComponentOption | ScatterSeriesOption>
const effectiveRiskChartOptions = computed<effectiveRiskChartOptionsType>(() => {
  const scatterData: ScatterSeriesOption['data'] = (effectiveRiskDataList.value || []).map(item => {
    const status =
      item.status === 1
        ? '恶化'
        : item.status === 2
        ? '不变'
        : item.status === 3
        ? '改善'
        : item.status === 4
        ? '结束'
        : ''

    const conflictIcon = item.level === 1 ? conflict1 : item.level === 2 ? conflict2 : item.level === 3 ? conflict3 : ''
    const naturalDisasterIcon =
      item.level === 1
        ? naturalDisaster1
        : item.level === 2
        ? naturalDisaster2
        : item.level === 3
        ? naturalDisaster3
        : ''

    return {
      name: item.riskName,
      value: [item.longitude, item.latitude],
      itemStyle: {
        color: item.level === 1 ? '#D92400' : item.level === 2 ? '#fe6b00' : '#faad14'
      },
      symbol: `image://${item.riskType === '自然灾害' ? naturalDisasterIcon : conflictIcon}`,
      symbolSize: item.riskType === '自然灾害' ? 18 : 22,
      tooltip: {
        show: true,
        padding: [8, 12],
        formatter: () => `
        <div class="flex items-start">
          <div>
            <img src="${item.riskType === '自然灾害' ? naturalDisasterIcon : conflictIcon}" class="w18px h18px" />
          </div>
          <div class="ml12px">
            <p class="mb8px">${item.riskType}-${item.riskSubType}</p>
            <p class="mb8px fs-18px fw500">${item.riskName}</p>
            <p>${status}</p>
          </div>
        </div>
        `
      }
    }
  })
  const options: effectiveRiskChartOptionsType = {
    tooltip: {
      trigger: 'item'
    },
    geo: {
      roam: true,
      map: 'world',
      zoom: 1.6,
      scaleLimit: { min: 1.3, max: 20 },
      nameProperty: 'name_zh',
      label: {
        show: false,
        color: '#333'
      },
      itemStyle: { areaColor: '#d4d4d4', borderColor: '#fff' },
      center: [15, 15],
      emphasis: {
        label: {
          show: true,
          color: '#333'
        },
        //悬停样式
        itemStyle: {
          areaColor: '#6553ee',
          borderColor: '#FFF'
        }
      },
      tooltip: { show: false }
      // projection: {
      //   project: point => [
      //     (point[0] / 180) * Math.PI,
      //     -Math.log(Math.tan((Math.PI / 2 + (point[1] / 180) * Math.PI) / 2))
      //   ],
      //   unproject: point => [(point[0] * 180) / Math.PI, ((2 * 180) / Math.PI) * Math.atan(Math.exp(point[1])) - 90]
      // }
    },
    series: [
      {
        type: 'scatter',
        coordinateSystem: 'geo',
        data: scatterData,
        symbolSize: 20
      }
    ]
  }
  return options
})

// 对我方影响
const riskImpactColumns = [
  { title: '内容', dataIndex: 'content', width: '30%' },
  { title: '类型', dataIndex: 'impactType', width: 80 },
  { title: '原因', dataIndex: 'reason', ellipsis: true },
  { title: '程度', dataIndex: 'level', width: 80, align: 'center' }
]
const riskImpactParams = ref({
  impactType: ''
})
const {
  dataList: riskImpactDataList,
  loading: riskImpactLoading,
  pageParams: riskImpactPageParams,
  refresh: getRiskImpactData
} = useListLoading(riskBoardRiskImpact, riskImpactParams, {
  immediateReqData: false,
  pageParams: { hideOnSinglePage: false, showSizeChanger: false, showTotal: () => null, pageSize: 30 }
})
function changeImpact(item: string) {
  riskImpactParams.value.impactType = item
  getRiskImpactData()
}

// 风险信息
const riskInfoColumns = [
  { title: '风险', dataIndex: 'riskName' },
  { title: '类型', dataIndex: 'riskSubType', width: '18%' },
  { title: '状态', dataIndex: 'status', width: '10%', align: 'center' },
  { title: '对我方影响', dataIndex: 'impactLevel', width: '14%', align: 'center' },
  { title: '等级', dataIndex: 'level', width: '10%', align: 'center' },
  { title: '告警时间', dataIndex: 'alarmBeginTime', width: 180 }
]
const {
  dataList: riskInfoDataList,
  pageParams: riskInfoPageParams,
  loading: riskInfoLoading,
  refresh: riskInfoGetData
} = useListLoading(
  riskBoardRiskInfo,
  {},
  { immediateReqData: false, pageParams: { hideOnSinglePage: false, showSizeChanger: false, showTotal: () => null } }
)

// 近3个月风险类型TOP10
const { dataList: top10DataList, loading: top10Loading } = useRequest(riskBoardRiskSubTypeTop)
type top10ChartOptionsType = ComposeOption<TooltipComponentOption | GridComponentOption | PieSeriesOption>
const top10ChartOptions = computed<top10ChartOptionsType>(() => {
  const options: top10ChartOptionsType = {
    tooltip: {
      trigger: 'item'
    },
    grid: { containLabel: true, left: 'left', right: 0, top: 0, bottom: 0 },
    legend: {
      bottom: 'bottom'
    },
    series: [
      {
        type: 'pie',
        radius: '60%',
        data: top10DataList.value?.map(item => ({ name: item.riskSubType || '', value: item.total || 0 })),
        center: ['50%', '45%'],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  return options
})
</script>

<style lang="less" scoped>
::v-deep(.ant-card) {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .ant-card-head {
    border: none;
    min-height: auto;
    padding: 16px 16px 0px;
  }
  .ant-card-body {
    overflow: auto;
  }

  .ant-spin-nested-loading,
  .ant-spin-container {
    height: 100%;
  }

  .ant-table-pagination {
    margin-bottom: 0;
  }
}
</style>
