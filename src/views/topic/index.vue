<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-12 17:09:58
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-21 18:11:58
 * @FilePath: /global-intelligence-web/src/views/topic/index.vue
 * @Description: 
-->
<template>
  <div class="bg-#fff flex h100%">
    <!-- <div class="w-260px border-c-#050505-6 border-r-1px border-solid">
      <GroupList v-model:activityGroupId="activityGroupId" />
    </div> -->
    <div class="flex-1">
      <MainTable />
    </div>
  </div>
</template>

<script setup lang="ts">
// import { ref } from 'vue'
// import GroupList from './components/groupList.vue'
import MainTable from './components/mainTable.vue'

// const activityGroupId = ref<string>('全部')
</script>

<style lang="less" scoped></style>
