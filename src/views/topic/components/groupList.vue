<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-31 10:28:17
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-18 14:42:34
 * @FilePath: /global-intelligence-web/src/views/topic/components/groupList.vue
 * @Description: 
-->
<template>
  <div class="h100% flex flex-direction-column">
    <div class="titleBox p-16px flex-between-center">
      <p class="fw-550 color-#000-88 fs-20">订阅</p>
    </div>

    <ul class="px-8px">
      <li
        :class="['groupItem clickGroupItem', props.activityGroupId === '全部' ? 'bg-#f4f0ff' : '']"
        @click="emits('update:activityGroupId', '全部')"
      >
        <p>
          <iconfontIcon icon="icon-format-horizontal-align-center"></iconfontIcon>
          全部
        </p>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import iconfontIcon from '@/components/tools/iconfontIcon'

const props = defineProps<{ activityGroupId: string }>()
const emits = defineEmits(['update:activityGroupId'])
</script>

<style lang="less" scoped>
.groupItem {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.88);
  border-radius: 6px;
  + .groupItem {
    margin-top: 8px;
  }
  p {
    @apply transition-all ellipsis;
    padding: 6px 0 6px 16px;
  }
}

.subGroupList {
  padding-left: 2em;
  padding-top: 8px;
  .groupItem {
    display: flex;
    align-items: center;
    padding-right: 8px;
    p {
      padding-left: 8px;
    }
  }
}

.clickGroupItem {
  cursor: pointer;
  &:hover {
    background-color: #ebebeb;
  }
}
</style>
