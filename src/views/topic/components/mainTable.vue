<template>
  <div class="">
    <div class="titleBox p-16px flex-between-center">
      <a-space>
        <div>
          <a-button type="primary" @click="goBack" ghost size="small">
            <div class="flex items-center">
              <iconfontIcon icon="icon-chevron-left" class="fs-14!" />
              <span>返回</span>
            </div>
          </a-button>
        </div>
        <p class="fw-550 color-#000-88 fs-20">订阅管理</p>
      </a-space>
      <div class="extra">
        <a-space>
          <PlusOutlined @click="createdModalRef?.onOpen" class="fs-20px cursor-pointer" />
          <a-popover
            placement="bottomLeft"
            trigger="click"
            :overlayInnerStyle="{ padding: '0px' }"
            v-model:open="filterPopoverVisible"
            @openChange="handleFilterPopoverVisibleChange"
          >
            <template #content>
              <a-card :bordered="false">
                <div class="w-400px">
                  <a-form :model="formData" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" autocomplete="off">
                    <a-form-item label="关键字" name="keyword">
                      <a-input v-model:value="formData.keyword" placeholder="主题名称" />
                    </a-form-item>
                    <a-form-item label="启用状态" name="monitorState">
                      <a-radio-group v-model:value="formData.monitorState">
                        <a-radio :value="1">启用</a-radio>
                        <a-radio :value="0">禁用</a-radio>
                      </a-radio-group>
                    </a-form-item>
                  </a-form>
                </div>
                <template #actions>
                  <div class="flex-between-center px16px">
                    <div>
                      <a-button type="link" @click="handleFilterReset">重置</a-button>
                    </div>
                    <div class="flex-1 text-right">
                      <a-space>
                        <a-button @click="filterPopoverVisible = false">取消</a-button>
                        <a-button type="primary" @click="handleFilterSubmit"> 筛选</a-button>
                      </a-space>
                    </div>
                  </div>
                </template>
              </a-card>
            </template>
            <a-badge :dot="!isUndefined(formData.keyword) || !isUndefined(formData.monitorState)">
              <iconfontIcon icon="icon-filter2" :extra-common-props="{ class: ['fs-22px hoverPrimaryColor'] }" />
            </a-badge>
          </a-popover>
        </a-space>
      </div>
    </div>
    <div class="p16px">
      <a-table
        :columns="columns"
        :data-source="dataList"
        :pagination="{
          ...pageParams,
          onChange: handlerSizeChange
        }"
        :loading="loading"
        size="small"
      >
        <template #bodyCell="{ text, column, record }: { text: string, column: ColumnType, record: NewsTopicType }">
          <template v-if="column.dataIndex === 'topicName'">
            <a @click="updateModalRef?.onOpen(record)"> {{ text }}</a>
          </template>
          <template v-if="column.dataIndex === 'monitorState'">
            <a-switch
              :checked="text"
              :checkedValue="1"
              :unCheckedValue="0"
              @change="(checked:number) => switchSubscribeStatus(checked, record)"
            ></a-switch>
          </template>
          <template v-if="column.dataIndex === 'actions'">
            <MoreIcon :menuList="menuList" @click="menuItem => handlerMoreIconClick(menuItem, record)" />
          </template>
        </template>
      </a-table>
    </div>

    <CreatedModal ref="createdModalRef" @refresh="getData" />
    <UpdateModal ref="updateModalRef" @refresh="getData" />
  </div>
</template>

<script setup lang="ts">
import { newsTopicDelete, newsTopicPage, newsTopicUpdate } from '@/api/api'
import MoreIcon, { type menuItem } from '@/components/tools/moreIcon.vue'
import useListLoading from '@/hooks/useListLoading'
import { PlusOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import type { ColumnsType, ColumnType } from 'ant-design-vue/es/table'
import { ref } from 'vue'
import type { NewsTopicType } from '~/types/api/newsTopic/common'
import type { NewsTopicPageReqType } from '~/types/api/newsTopic/page'
import CreatedModal from './topicDetail/createdModal.vue'
import UpdateModal from './topicDetail/updateModal.vue'
import type { MenuInfo } from 'ant-design-vue/es/menu/src/interface'
import { isUndefined } from 'lodash-es'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { useRouter } from 'vue-router'

const createdModalRef = ref<InstanceType<typeof CreatedModal>>()
const updateModalRef = ref<InstanceType<typeof UpdateModal>>()
const router = useRouter()

// 返回事件
async function goBack() {
  router.back()
}

/**
 * @description: 表单下拉筛选项相关
 */
function useFilter() {
  const formData = ref<NewsTopicPageReqType>({
    keyword: undefined,
    monitorState: undefined
  })

  const filterPopoverVisible = ref(false)

  const formDataReset = () => {
    formData.value = {
      keyword: undefined,
      monitorState: undefined
    }
  }

  function handleFilterPopoverVisibleChange(visible: boolean) {
    filterPopoverVisible.value = visible
  }

  return {
    formData,
    filterPopoverVisible,
    formDataReset,
    handleFilterPopoverVisibleChange
  }
}

const { formData, filterPopoverVisible, formDataReset, handleFilterPopoverVisibleChange } = useFilter()

function handleFilterSubmit() {
  handleFilterPopoverVisibleChange(false)
  refresh()
}
function handleFilterReset() {
  formDataReset()
}

const { dataList, refresh, getData, pageParams, loading } = useListLoading(newsTopicPage, formData)
const columns: ColumnsType = [
  { title: '主题', dataIndex: 'topicName' },
  { title: '推送数量(近30日)', dataIndex: 'lastThirtyTotal' },
  { title: '启用状态', dataIndex: 'monitorState' },
  { title: '', dataIndex: 'actions', width: '64px' }
]

function handlerSizeChange(pageNo: number, pageSize: number) {
  pageParams.value.current = pageNo
  pageParams.value.pageSize = pageSize
  getData()
}

// 更多按钮
const menuList: menuItem[] = [
  { title: '编辑', key: 'edit' },
  { title: '删除', key: 'delete' }
]
// 更多按钮点击事件
function handlerMoreIconClick(clickItem: MenuInfo, record: NewsTopicType) {
  switch (clickItem.key) {
    case 'edit':
      updateModalRef.value?.onOpen(record)
      break
    case 'delete':
      Modal.confirm({
        title: '确定删除该专题吗？',
        content: '删除后不可恢复',
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            loading.value = true
            const { message: msg } = await newsTopicDelete({ id: record.id! })
            message.success(msg)
            await getData()
            loading.value = false
          } catch (error) {
            console.error(error)
            loading.value = false
          }
        }
      })

      break

    default:
      break
  }
}

// 切换专题的订阅状态
async function switchSubscribeStatus(status: number, record: NewsTopicType) {
  try {
    loading.value = true
    const { message: msg } = await newsTopicUpdate({
      id: record.id!,
      topicName: record.topicName,
      websiteIds: record.websiteIds,
      tagConfig: record.tagConfig,
      // brandList: record.brandList,
      monitorState: status
    })
    message.success(msg)
    await getData()
    loading.value = false
  } catch (error) {
    loading.value = false
    console.error(error)
  }
}
</script>

<style lang="less" scoped></style>
