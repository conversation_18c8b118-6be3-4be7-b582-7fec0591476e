<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-14 10:19:14
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-11 11:59:56
 * @FilePath: /global-intelligence-web/src/views/topic/components/topicDetail/dataSource.vue
 * @Description: 
-->
<template>
  <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
    <a-form-item label="主题" name="topicName">
      <a-input placeholder="主题" v-model:value="formData.topicName"></a-input>
    </a-form-item>
    <a-form-item name="websiteIds">
      <template #label>
        <a-space>
          <p>数据源</p>
          <a-form-item-rest>
            <a-checkbox
              v-model:checked="formData.websiteIdsNoLimit"
              @change="(e:CheckboxChangeEvent) => handleNoLimitClick('websiteIds', e)"
            >
              不限
            </a-checkbox>
          </a-form-item-rest>
        </a-space>
      </template>

      <a-form-item-rest>
        <a-input
          placeholder="站点名称"
          v-model:value="websiteParams.keyword"
          class="mb16px"
          @change="handleWebsiteSearch"
          :disabled="formData.websiteIdsNoLimit"
          allowClear
        ></a-input>
      </a-form-item-rest>

      <a-row class="h500px overflow-hidden">
        <a-col :span="16" class="h100% flex flex-col">
          <div class="flex">
            <div
              v-for="(item, index) in typeMap"
              :key="index"
              :class="[
                'flex-center-center flex-col  w56px text-center',
                index !== 0 ? 'ml16px' : '',
                item.value === websiteParams.type ? 'color-#6553ee' : '',
                formData.websiteIdsNoLimit ? 'disabledTag ' : 'hoverPrimaryColor'
              ]"
              @click="changeWebsiteType(item.value)"
            >
              <iconfontIcon :icon="item.icon" :extra-common-props="{ style: { fontSize: '32px' } }" />
              {{ item.label }}
            </div>
          </div>

          <div class="mt16px flex-1 overflow-auto">
            <a-tag
              v-for="item in websiteOptions"
              :key="item.id"
              @click="handleTagClick(item)"
              :class="[
                'tag',
                formData.websiteIdsNoLimit ? 'disabledTag' : 'checkableTag',
                formData.websiteIds?.includes(item.id) ? 'checkedTag' : ''
              ]"
            >
              {{ item.name }}
            </a-tag>

            <div
              v-if="websiteOptions.length > 0"
              class="loadMore"
              v-intersection-observer="handlerIntersectionObserver"
            >
              <p class="endText">
                <template v-if="!websiteNoMore">
                  <a-spin tip="加载中..."></a-spin>
                </template>
                <template v-else>没有更多数据</template>
              </p>
            </div>
          </div>
        </a-col>
        <a-col :span="8" class="h100%">
          <div class="h100% flex">
            <a-divider type="vertical" style="height: 100%"></a-divider>
            <div class="flex-1 overflow-auto">
              <a-spin :spinning="selectedWebsitesLoading">
                <a-tag
                  v-for="item in selectedWebsites"
                  :key="item.id"
                  closable
                  @close="handleTagClose(item.id)"
                  class="tag"
                >
                  {{ item.name }}
                </a-tag>
                <div v-if="!formData.websiteIds?.length" class="h100% flex-center-center">
                  <a-empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
                </div>
              </a-spin>
            </div>
          </div>
        </a-col>
      </a-row>
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import { websitePage, websiteTopic } from '@/api/api'
import { Empty, theme, type FormInstance } from 'ant-design-vue'
import { debounce, isUndefined } from 'lodash-es'
import { computed, ref, toRef, watch, watchEffect } from 'vue'
import type { TopicDataType } from './types/common'
import { useVModel } from '@vueuse/core'
import { vIntersectionObserver } from '@vueuse/components'
import type { Rule } from 'ant-design-vue/es/form'
import type { CheckboxChangeEvent } from 'ant-design-vue/es/checkbox/interface'
import type { websitePageReqType, WebsiteVo } from '~/types/api/website/page'
import iconfontIcon from '@/components/tools/iconfontIcon'
import useInfiniteLoading from '@/hooks/useInfiniteLoading'
import useRequest from '@/hooks/useRequest'

// Theme相关
const { useToken } = theme
const { token: themeToken } = useToken()

// Props & Emits
const props = withDefaults(
  defineProps<{
    dataSourceInfo: TopicDataType
    type?: 'add' | 'edit'
  }>(),
  { type: 'add' }
)
const emits = defineEmits<{
  'update:dataSourceInfo': [val: TopicDataType]
}>()

// Form相关
const formData = useVModel(props, 'dataSourceInfo', emits)
const formRef = ref<FormInstance>()
const rules: Record<string, Rule[]> = {
  topicName: [{ required: true, message: '请输入主题名称', trigger: 'blur' }],
  websiteIds: [
    {
      required: false,
      trigger: 'change',
      validator: (_rule, value) => {
        const isValid = formData.value.websiteIdsNoLimit || value.length > 0
        return !isValid ? Promise.reject('请选择数据源') : Promise.resolve()
      }
    }
  ]
}
function refreshForm() {
  selectedWebsites.value = []
  formRef.value?.resetFields()
}

// 站点类型相关
const websiteParams = ref<websitePageReqType>({ keyword: undefined, type: undefined })
const typeMap = [
  { label: '全部站点', value: undefined, icon: 'icon-dingyue1' },
  { label: '政策公告', value: '政策公告', icon: 'icon-dingyue1' },
  { label: '行业标准', value: '行业标准', icon: 'icon-dingyue1' },
  { label: '资讯', value: '资讯', icon: 'icon-dingyue1' },
  { label: '内部通知', value: '内部通知', icon: 'icon-dingyue1' },
  { label: '其它', value: '其它', icon: 'icon-dingyue1' }
]
function changeWebsiteType(type: string | undefined) {
  websiteParams.value.type = type
  websiteOptions.value = []
  refresh()
}

// 站点列表相关
const {
  dataList: websiteOptions,
  noMore: websiteNoMore,
  loading: websiteLoading,
  onLoadMore,
  refresh
} = useInfiniteLoading(websitePage, websiteParams, {
  pageParams: { pageSize: 200 }
})
const handleWebsiteSearch = debounce(() => {
  websiteOptions.value = []
  refresh()
}, 300)
function handlerIntersectionObserver(intersectionObserverList: IntersectionObserverEntry[]) {
  const { isIntersecting } = intersectionObserverList[0]
  if (isIntersecting && !websiteLoading.value && !websiteNoMore.value) {
    onLoadMore()
  }
}

// 选中站点相关
const selectedWebsites = ref<WebsiteVo[]>([])
const selectedWebsitesLoading = ref(false)
function handleTagClick(tagItem: WebsiteVo) {
  if (formData.value.websiteIdsNoLimit) {
    return
  }
  const index = formData.value.websiteIds?.findIndex(item => item === tagItem.id)
  if (isUndefined(index)) {
    return
  }
  if (index !== -1) {
    formData.value.websiteIds?.splice(index, 1)
    const selectedWebsitesIndex = selectedWebsites.value.findIndex(item => item.id === tagItem.id)
    selectedWebsites.value.splice(selectedWebsitesIndex, 1)
  } else {
    formData.value.websiteIds?.push(tagItem.id)
    selectedWebsites.value.push(tagItem)
  }
}
function handleTagClose(key: string) {
  const index = formData.value.websiteIds?.findIndex(item => item === key)
  const selectedWebsitesIndex = selectedWebsites.value?.findIndex(item => item.id === key)
  if (index !== -1) {
    formData.value.websiteIds?.splice(index, 1)
    selectedWebsites.value?.splice(selectedWebsitesIndex, 1)
  }
}
function handleNoLimitClick(key: string, e: CheckboxChangeEvent) {
  formData.value.websiteIds = []
  selectedWebsites.value = []
}

// 编辑模式下获取已选站点数据
watchEffect(() => {
  if (props.dataSourceInfo.id && props.type === 'edit') {
    getSelectedWebsitesData()
  }
})
async function getSelectedWebsitesData() {
  try {
    selectedWebsitesLoading.value = true
    if (!props.dataSourceInfo.id) return
    const { result } = await websiteTopic(props.dataSourceInfo.id)
    selectedWebsites.value = result
    selectedWebsitesLoading.value = false
  } catch (error) {
    selectedWebsitesLoading.value = false
    console.error(error)
  }
}

// 导出
defineExpose({ formData, formRef, refreshForm })
</script>

<style lang="less" scoped>
.tag {
  margin: 0 4px 4px 0;
}
.checkableTag {
  cursor: pointer;
  &:hover {
    background-color: rgba(0, 0, 0, 0.06);
    color: v-bind('themeToken.colorPrimary');
  }
}
.checkedTag {
  background-color: v-bind('themeToken.colorPrimary');
  color: #fff;
  &:hover {
    color: #fff;
    background-color: v-bind('themeToken.colorPrimaryHover');
  }
}

.disabledTag {
  cursor: not-allowed;
  color: v-bind('themeToken.colorTextDisabled');
}
</style>
