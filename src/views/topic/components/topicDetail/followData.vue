<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-14 10:19:14
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-04-28 18:32:56
 * @FilePath: /global-intelligence-web/src/views/topic/components/topicDetail/followData.vue
 * @Description: 
-->
<template>
  <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
    <!-- 行业 -->
    <a-form-item name="brand">
      <template #label>
        <a-space>
          <p>品牌</p>
          <a-form-item-rest>
            <a-checkbox
              v-model:checked="formData.brandNoLimit"
              @change="(e:CheckboxChangeEvent) => handleNoLimitClick('brand', e)"
            >
              不限
            </a-checkbox>
          </a-form-item-rest>
        </a-space>
      </template>
      <a-select
        v-model:value="formData.brand"
        placeholder="品牌"
        mode="multiple"
        :options="getTag('brand')"
        :fieldNames="{ label: 'labelName', value: 'id' }"
        show-search
        :filter-option="(input: string, option: any) => option.labelName.toLowerCase().indexOf(input.toLowerCase()) >= 0"
        :disabled="formData.brandNoLimit"
      >
      </a-select>
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import { useTagStore } from '@/store'
import { useVModel } from '@vueuse/core'
import type { FormExpose } from 'ant-design-vue/es/form/Form'
import { useTemplateRef } from 'vue'
import type { TopicDataType } from './types/common'
import type { Rule } from 'ant-design-vue/es/form'
import type { CheckboxChangeEvent } from 'ant-design-vue/es/_util/EventInterface'

const { getTag } = useTagStore()

const props = withDefaults(defineProps<{ tagConfig: TopicDataType['tagConfig']; type?: 'edit' | 'add' }>(), {
  type: 'add'
})
const emits = defineEmits(['update:tagConfig'])

const formRef = useTemplateRef<FormExpose>('formRef')
const formData = useVModel(props, 'tagConfig', emits)

const rules: Record<string, Rule[]> = {
  brand: [
    {
      required: false,
      trigger: 'change',
      validator: (_rule, value) => {
        const isValid = formData.value.brandNoLimit || value.length > 0
        return !isValid ? Promise.reject('请选择') : Promise.resolve()
      }
    }
  ]
}

function refreshForm() {
  formRef.value?.resetFields()
}

function handleNoLimitClick(key: string, e: CheckboxChangeEvent) {
  formData.value.brand = []
}

defineExpose({ formData, formRef, refreshForm })
</script>

<style scoped></style>
