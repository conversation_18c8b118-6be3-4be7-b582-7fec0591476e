export interface TopicDataType {
  id?: string
  /** 专题名称 */
  topicName: string
  /** 站点id */
  websiteIds: string[]
  /** 站点不限 */
  websiteIdsNoLimit: boolean
  /** 数据标签 */
  tagConfig: {
    /** 行业 */
    industry: string[]
    /** 行业不限 */
    industryNoLimit: boolean
    /** 事件 */
    event: string[]
    /** 事件不限 */
    eventNoLimit: boolean
    /** 国家 */
    continent_country: string[]
    /** 国家不限 */
    continent_countryNoLimit: boolean
    /** 品牌 */
    brand: string[]
    /** 品牌不限 */
    brandNoLimit: boolean
  }
}
