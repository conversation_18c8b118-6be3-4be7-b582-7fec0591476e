<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-14 10:21:11
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-04-07 11:07:32
 * @FilePath: /global-intelligence-web/src/views/topic/components/topicDetail/updateModal.vue
 * @Description: 
-->
<template>
  <a-modal
    v-model:open="open"
    :confirmLoading="confirmLoading"
    title="编辑订阅内容"
    :width="1200"
    @cancel="handleCancel"
    @ok="handleOk"
    forceRender
  >
    <!-- v-model:topicName="topicData.topicName"
  v-model:websiteIds="topicData.websiteIds" -->
    <DataSource ref="dataSourceRef" v-model:dataSourceInfo="topicData" type="edit" />
    <DataLabel ref="dataLabelRef" v-model:tagConfig="topicData.tagConfig" type="edit" />
    <FollowData ref="followDataRef" v-model:tagConfig="topicData.tagConfig" type="edit" />
  </a-modal>
</template>

<script setup lang="ts">
import { nextTick, ref, useTemplateRef } from 'vue'
import DataLabel from './dataLabel.vue'
import DataSource from './dataSource.vue'
import FollowData from './followData.vue'
import type {
  MapListSystemLabelVo,
  NewsTopicType,
  SystemLabelVo,
  UpdateNewsTopicType
} from '~/types/api/newsTopic/common'
import { newsTopicUpdate } from '@/api/api'
import { message } from 'ant-design-vue'
import type { systemLabelType } from '~/types/api/system/getSystemLabel'
import { isUndefined } from 'lodash-es'
import { useTagStore } from '@/store'
import type { TopicDataType } from './types/common'

const { getTag } = useTagStore()

const emits = defineEmits(['refresh'])
const dataSourceRef = useTemplateRef('dataSourceRef')
const dataLabelRef = useTemplateRef('dataLabelRef')
const followDataRef = useTemplateRef('followDataRef')
const confirmLoading = ref(false)

const topicData = ref<TopicDataType>({
  id: '',
  topicName: '',
  websiteIds: [],
  websiteIdsNoLimit: false,
  tagConfig: {
    industry: [],
    industryNoLimit: false,
    event: [],
    eventNoLimit: false,
    continent_country: [],
    continent_countryNoLimit: false,
    brand: [],
    brandNoLimit: false
  }
})

const open = ref(false)

function onOpen(editData: NewsTopicType) {
  console.log('editData: ', editData)

  const tagConfig = transformResDataFormatToTagConfigTo({
    industry: editData.tagConfig['industry'] || [],
    event: editData.tagConfig['event'] || [],
    continent_country: editData.tagConfig['continent_country'] || [],
    brand: editData.tagConfig['brand'] || []
  })

  topicData.value = {
    id: editData.id!,
    topicName: editData.topicName,
    websiteIds: editData.websiteIds,
    websiteIdsNoLimit: editData.websiteIds.length === 0,
    tagConfig: {
      industry: tagConfig['industry'],
      industryNoLimit: tagConfig['industry'].length === 0,
      event: tagConfig['event'],
      eventNoLimit: tagConfig['event'].length === 0,
      continent_country: tagConfig['continent_country'],
      continent_countryNoLimit: tagConfig['continent_country'].length === 0,
      brand: tagConfig['brand'],
      brandNoLimit: tagConfig['brand'].length === 0
    }
  }
  console.log('topicData.value: ', topicData.value)
  open.value = true
}

function handleCancel() {
  open.value = false
  nextTick(() => {
    dataSourceRef.value?.refreshForm()
    dataLabelRef.value?.refreshForm()
    followDataRef.value?.refreshForm()
    topicData.value = {
      id: '',
      topicName: '',
      websiteIdsNoLimit: false,
      websiteIds: [],
      tagConfig: {
        industry: [],
        industryNoLimit: false,
        event: [],
        eventNoLimit: false,
        continent_country: [],
        continent_countryNoLimit: false,
        brand: [],
        brandNoLimit: false
      }
      // brandList: []
    }
  })
}
async function handleOk() {
  try {
    await Promise.all([
      dataSourceRef.value?.formRef!.validate(),
      dataLabelRef.value?.formRef!.validate(),
      followDataRef.value?.formRef!.validate()
    ])

    confirmLoading.value = true
    console.log('topicData.value: ', topicData.value)
    const params: UpdateNewsTopicType = {
      ...topicData.value,
      id: topicData.value.id as string,
      tagConfig: transformTagConfigToReqDataFormat({
        industry: topicData.value.tagConfig.industry as string[],
        event: topicData.value.tagConfig.event as string[],
        continent_country: topicData.value.tagConfig.continent_country as string[],
        brand: topicData.value.tagConfig.brand as string[]
      })
    }
    console.log('params: ', params)
    const { message: msg } = await newsTopicUpdate(params)
    message.success(msg)
    confirmLoading.value = false
    handleCancel()
    emits('refresh')
  } catch (error) {
    confirmLoading.value = false
    console.error(error)
  }
}

/**
 * @description: 将传入的{id,labelName,subList}的格式转换成id格式
 * @param {*} arr
 * @param {*} key 标签数据的key
 * @return {*}
 */
function transformResDataFormatToTagConfigTo(tagConfig: MapListSystemLabelVo) {
  function transformTag(arr: systemLabelType[], key: string): string[] {
    if (!Array.isArray(arr)) return []
    let returnData: string[] = []
    switch (key) {
      case 'continent_country':
        arr.forEach(item => returnData.push(...(item.subList || []).map(subItem => subItem.id)))
        break

      default:
        returnData = arr.map(item => item.id)
        break
    }
    return returnData
  }
  const temp: Record<string, string[]> = {}
  Object.keys(tagConfig).forEach(tagKey => {
    const tagItem = tagConfig[tagKey]
    temp[tagKey] = transformTag(tagItem, tagKey)
  })
  return temp
}

/**
 * @description: 将topicData转化为接口需要的数据格式
 * @param {*} tagConfig
 * @param {*} string
 * @return {*}
 */
function transformTagConfigToReqDataFormat(tagConfig: Record<string, string[]>): MapListSystemLabelVo {
  /**
   * @description: 根据传入的tagId转换成{id,labelName,subList}的格式
   * @param {*} key 标签数据的key
   * @param {*} ids
   * @param {*} _labelMap
   * @return {*}
   */
  function findTag(key: string, ids: string[], _labelMap: systemLabelType[]): SystemLabelVo[] {
    const forData = _labelMap
    let returnData: systemLabelType[] = []

    for (let index = 0; index < forData.length; index++) {
      const item = forData[index]
      // 判断有没有子级
      if (item.subList && item.subList.length) {
        // 递归
        const tempSubData = findTag(key, ids, item.subList)
        if (tempSubData.length) {
          returnData.push({ ...item, subList: tempSubData })
        }
      }
      // 判断有没有id
      if (ids.includes(item.id)) {
        returnData.push({ id: item.id, labelName: item.labelName })
      }
    }
    return returnData
  }

  const temp: MapListSystemLabelVo = {}
  Object.keys(tagConfig).forEach(tagKey => {
    const tagItem = tagConfig[tagKey]
    const mapData = getTag(tagKey) // 获取标签数据
    if (isUndefined(mapData)) {
      console.error('mapData为空')
      return false
    }
    console.log('temp: ', findTag(tagKey, tagItem, mapData))
    temp[tagKey] = findTag(tagKey, tagItem, mapData)
  })
  return temp
}

defineExpose({
  onOpen
})
</script>

<style scoped></style>
