<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-14 10:19:14
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-04-03 11:16:37
 * @FilePath: /global-intelligence-web/src/views/topic/components/topicDetail/dataLabel.vue
 * @Description: 
-->
<template>
  <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
    <!-- 行业 -->
    <a-form-item name="industry">
      <template #label>
        <a-space>
          <p>行业</p>
          <a-form-item-rest>
            <a-checkbox
              v-model:checked="formData.industryNoLimit"
              @change="(e:CheckboxChangeEvent) => handleNoLimitClick('industry', e)"
            >
              不限
            </a-checkbox>
          </a-form-item-rest>
        </a-space>
      </template>

      <a-checkbox-group
        v-model:value="formData.industry"
        :disabled="formData.industryNoLimit"
        @change="handleCheckboxChange"
      >
        <a-checkbox v-for="item in getTag('industry')" :key="item.id" :value="item.id">
          {{ item.labelName }}
        </a-checkbox>
      </a-checkbox-group>
    </a-form-item>

    <!-- 事件类型 -->
    <a-form-item name="event">
      <template #label>
        <a-space>
          <p>事件类型</p>
          <a-form-item-rest>
            <a-checkbox
              v-model:checked="formData.eventNoLimit"
              @change="(e:CheckboxChangeEvent) => handleNoLimitClick('event', e)"
            >
              不限
            </a-checkbox>
          </a-form-item-rest>
        </a-space>
      </template>

      <a-checkbox-group v-model:value="formData.event" :disabled="formData.eventNoLimit">
        <a-checkbox v-for="item in getTag('event')" :key="item.id" :value="item.id">
          {{ item.labelName }}
        </a-checkbox>
      </a-checkbox-group>
    </a-form-item>

    <!-- 国家 -->
    <a-form-item name="continent_country">
      <template #label>
        <a-space>
          <p>国家</p>
          <a-form-item-rest>
            <a-checkbox
              v-model:checked="formData.continent_countryNoLimit"
              @change="(e:CheckboxChangeEvent) => handleNoLimitClick('continent_country', e)"
            >
              不限
            </a-checkbox>
          </a-form-item-rest>
        </a-space>
      </template>

      <a-tree-select
        v-model:value="formData.continent_country"
        style="width: 100%"
        :dropdown-style="{ maxHeight: '260px', overflow: 'auto' }"
        placeholder="国家"
        treeCheckable
        :showCheckedStrategy="TreeSelect.SHOW_CHILD"
        :maxTagCount="10"
        placement="bottomLeft"
        tree-node-filter-prop="labelName"
        :fieldNames="{ label: 'labelName', value: 'id', children: 'subList' }"
        :tree-data="getTag('continent_country')"
        treeDefaultExpandAll
        :disabled="formData.continent_countryNoLimit"
      >
      </a-tree-select>
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import { useTagStore } from '@/store'
import { useVModel } from '@vueuse/core'
import { TreeSelect } from 'ant-design-vue'
import type { FormExpose } from 'ant-design-vue/es/form/Form'
import { useTemplateRef } from 'vue'
import type { TopicDataType } from './types/common'
import type { CheckboxChangeEvent, CheckboxValueType } from 'ant-design-vue/es/checkbox/interface'
import type { Rule } from 'ant-design-vue/es/form'

const { getTag } = useTagStore()

const props = withDefaults(defineProps<{ tagConfig: TopicDataType['tagConfig']; type?: 'edit' | 'add' }>(), {
  type: 'add'
})
const emits = defineEmits(['update:tagConfig'])

const formRef = useTemplateRef<FormExpose>('formRef')
const formData = useVModel(props, 'tagConfig', emits)

const rules: Record<string, Rule[]> = {
  industry: [
    {
      required: false,
      trigger: 'change',
      validator: (_rule, value) => {
        const isValid = formData.value.industryNoLimit || value.length > 0
        return !isValid ? Promise.reject('请选择') : Promise.resolve()
      }
    }
  ],
  event: [
    {
      required: false,
      trigger: 'change',
      validator: (_rule, value) => {
        const isValid = formData.value.eventNoLimit || value.length > 0
        return !isValid ? Promise.reject('请选择') : Promise.resolve()
      }
    }
  ],
  continent_country: [
    {
      required: false,
      trigger: 'change',
      validator: (_rule, value) => {
        const isValid = formData.value.continent_countryNoLimit || value.length > 0
        return !isValid ? Promise.reject('请选择') : Promise.resolve()
      }
    }
  ]
}

function refreshForm() {
  formRef.value?.resetFields()
}

function handleNoLimitClick(key: string, e: CheckboxChangeEvent) {
  switch (key) {
    case 'industry':
      formData.value.industry = []
      break
    case 'event':
      formData.value.event = []
      break
    case 'continent_country':
      formData.value.continent_country = []
      break
    default:
      break
  }
}

function handleCheckboxChange(e: CheckboxValueType[]) {
  console.log('e: ', e)
}

defineExpose({ formData, formRef, refreshForm })
</script>

<style scoped></style>
