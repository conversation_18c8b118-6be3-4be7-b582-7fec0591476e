<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-14 10:21:11
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-11 11:58:19
 * @FilePath: /global-intelligence-web/src/views/topic/components/topicDetail/createdModal.vue
 * @Description: 
-->
<template>
  <a-modal :open="open" title="新增订阅内容" :width="1000" @cancel="handleCancel('closeBtn')" forceRender >
    <template #footer>
      <a-button @click="handleCancel('cancelBtn')">{{ cancelText }}</a-button>
      <a-button type="primary" @click="handleOk" :loading="confirmLoading">{{ okText }}</a-button>
    </template>
    <a-steps
      :current="stepCurrent"
      label-placement="vertical"
      :items="[{ title: '选择数据源' }, { title: '数据标签' }, { title: '关注对象' }]"
    ></a-steps>
    <div class="pt16px">
      <!-- 选择数据源 -->
      <DataSource v-show="stepCurrent === 0" ref="dataSourceRef" v-model:dataSourceInfo="topicData" />
      <!-- v-model:topicName="topicData.topicName"
        v-model:websiteIds="topicData.websiteIds" -->
      <!-- 数据标签 -->
      <DataLabel v-show="stepCurrent === 1" ref="dataLabelRef" v-model:tagConfig="topicData.tagConfig" />
      <!-- 关注对象 -->
      <FollowData v-show="stepCurrent === 2" ref="followDataRef" v-model:tagConfig="topicData.tagConfig" />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, ref, useTemplateRef } from 'vue'
import DataSource from './dataSource.vue'
import DataLabel from './dataLabel.vue'
import FollowData from './followData.vue'
import { newsTopicCreate } from '@/api/api'
import { message } from 'ant-design-vue'
import type { CreatedNewsTopicType, MapListSystemLabelVo, SystemLabelVo } from '~/types/api/newsTopic/common'
import type { systemLabelType } from '~/types/api/system/getSystemLabel'
import { useTagStore } from '@/store'
import { isUndefined } from 'lodash-es'
import type { TopicDataType } from './types/common'

const { getTag } = useTagStore()
const emits = defineEmits(['refresh'])
const dataSourceRef = useTemplateRef('dataSourceRef')
const dataLabelRef = useTemplateRef('dataLabelRef')
const followDataRef = useTemplateRef('followDataRef')

const topicData = ref<TopicDataType>({
  topicName: '',
  websiteIds: [],
  websiteIdsNoLimit: false,
  tagConfig: {
    industry: [],
    industryNoLimit: false,
    event: [],
    eventNoLimit: false,
    continent_country: [],
    continent_countryNoLimit: false,
    brand: [],
    brandNoLimit: false
  }
})

const open = ref(false)
const confirmLoading = ref(false)
const stepCurrent = ref(0)
const cancelText = computed(() => (stepCurrent.value === 0 ? '取消' : '上一步'))
const okText = computed(() => (stepCurrent.value === 2 ? '创建' : '下一步'))

function onOpen() {
  open.value = true
}

function handleCancel(e: 'closeBtn' | 'cancelBtn') {
  if (stepCurrent.value === 0 || e === 'closeBtn') {
    topicData.value = {
      topicName: '',
      websiteIds: [],
      websiteIdsNoLimit: false,
      tagConfig: {
        industry: [],
        industryNoLimit: false,
        event: [],
        eventNoLimit: false,
        continent_country: [],
        continent_countryNoLimit: false,
        brand: [],
        brandNoLimit: false
      }
      // brandList: []
    }
    stepCurrent.value = 0
    dataSourceRef.value?.refreshForm()
    dataLabelRef.value?.refreshForm()
    followDataRef.value?.refreshForm()
    open.value = false
  } else {
    stepCurrent.value--
  }
}
async function handleOk() {
  try {
    console.log('topicData.value: ', topicData.value)
    await validateCompForm()
    if (stepCurrent.value !== 2) {
      stepCurrent.value++
    } else {
      confirmLoading.value = true
      const params: CreatedNewsTopicType = {
        ...topicData.value,
        tagConfig: transformTagConfigToReqDataFormat({
          industry: topicData.value.tagConfig.industry as string[],
          event: topicData.value.tagConfig.event as string[],
          continent_country: topicData.value.tagConfig.continent_country as string[],
          brand: topicData.value.tagConfig.brand as string[]
        })
      }
      console.log('params: ', params)
      const { message: msg } = await newsTopicCreate(params)
      message.success(msg)
      confirmLoading.value = false
      handleCancel('closeBtn')
      emits('refresh')
    }
  } catch (error) {
    confirmLoading.value = false
    console.error(error)
  }
}

function validateCompForm() {
  if (stepCurrent.value === 0) {
    return dataSourceRef.value?.formRef!.validate()
  } else if (stepCurrent.value === 1) {
    return dataLabelRef.value?.formRef!.validate()
  } else if (stepCurrent.value === 2) {
    return followDataRef.value?.formRef!.validate()
  }
}

/**
 * @description: 将topicData转化为接口需要的数据格式
 * @param {*} tagConfig
 * @param {*} string
 * @return {*}
 */
function transformTagConfigToReqDataFormat(tagConfig: Record<string, string[]>): MapListSystemLabelVo {
  /**
   * @description: 根据传入的tagId转换成{id,labelName,subList}的格式
   * @param {*} key 标签数据的key
   * @param {*} ids
   * @param {*} _labelMap
   * @return {*}
   */
  function findTag(key: string, ids: string[], _labelMap: systemLabelType[]): SystemLabelVo[] {
    const forData = _labelMap
    let returnData: systemLabelType[] = []

    for (let index = 0; index < forData.length; index++) {
      const item = forData[index]
      // 判断有没有子级
      if (item.subList && item.subList.length) {
        // 递归
        const tempSubData = findTag(key, ids, item.subList)
        if (tempSubData.length) {
          returnData.push({ ...item, subList: tempSubData })
        }
      }
      // 判断有没有id
      if (ids.includes(item.id)) {
        returnData.push({ id: item.id, labelName: item.labelName })
      }
    }
    return returnData
  }

  const temp: MapListSystemLabelVo = {}
  Object.keys(tagConfig).forEach(tagKey => {
    const tagItem = tagConfig[tagKey]
    const mapData = getTag(tagKey) // 获取标签数据
    if (isUndefined(mapData)) {
      console.error('mapData为空')
      return false
    }
    console.log('temp: ', findTag(tagKey, tagItem, mapData))
    temp[tagKey] = findTag(tagKey, tagItem, mapData)
  })
  return temp
}

defineExpose({
  onOpen
})
</script>

<style scoped></style>
