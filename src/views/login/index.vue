<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-09 16:46:11
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-18 10:28:45
 * @FilePath: /global-intelligence-web/src/views/login/index.vue
 * @Description: 
-->
<template>
  <div class="w100vw h100vh min-w-1280px min-h720px relative flex-center-start fs-14px bg-#f1f5f8">
    <Login-bg />
    <LoginForm />
  </div>
</template>

<script setup lang="ts" name="defaultLogin">
import LoginBg from './components/LoginBg.vue'
import LoginForm from './components/loginForm.vue'
</script>

<style lang="less" scoped></style>
