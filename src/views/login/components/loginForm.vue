<template>
  <div class="loginForm">
    <a type="link" target="_blank" href="https://www.bengine.com.cn/" class="officialWebsiteLink">前往官网</a>
    <div class="formWarp">
      <!-- <img :src="productLogo" class="productLogo" /> -->
      <h1 class="fs-42px pb-16px color-#6553ee">全球商业情报</h1>

      <a-form
        ref="formRef"
        class="form"
        name="login"
        autocomplete="off"
        :model="loginForm"
        :rules="rules"
        @finish="onFinish"
      >
        <a-form-item name="username">
          <a-input v-model:value="loginForm.username" placeholder="用户名">
            <template #prefix>
              <iconfontIcon icon="icon-user" class="site-form-item-icon"></iconfontIcon>
            </template>
          </a-input>
        </a-form-item>

        <a-form-item name="password">
          <a-input-password v-model:value="loginForm.password" class="password" placeholder="密码">
            <template #prefix>
              <iconfontIcon icon="icon-lock-on" class="site-form-item-icon"></iconfontIcon>
            </template>
          </a-input-password>
        </a-form-item>

        <a-form-item>
          <a-button size="large" type="primary" html-type="submit" class="loginBtn" :loading="loginBtnLoading">
            确定
          </a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import iconfontIcon from '@/components/tools/iconfontIcon'
import { type FormInstance, notification } from 'ant-design-vue'
import { ref } from 'vue'
import { timeFix } from '@/utils/util'
import { useRouter } from 'vue-router'
import type { Rule } from 'ant-design-vue/es/form'
// import productLogo from '@/assets/productLogo/default/logo.svg'
import { useUserStore } from '@/store'
import { sysLogin } from '@/api/api'
import type { SysLoginReqType } from '~/types/api/sys/login'

const router = useRouter()
const userStore = useUserStore()

const formRef = ref<FormInstance>()
const loginBtnLoading = ref(false)

const loginForm = ref<SysLoginReqType>({
  username: '',
  password: ''
})
const rules: Record<string, Rule[]> = {
  username: [{ required: true, message: '请输入' }],
  password: [{ required: true, message: '请输入' }]
}

// 登录成功
async function onFinish(values: SysLoginReqType) {
  try {
    loginBtnLoading.value = true
    const loginParams = { ...values, checkKey: new Date().getTime() }
    const { result } = await sysLogin(loginParams)
    await userStore.login(result.token, result.userVo)
    notification.success({ message: '欢迎', description: `${timeFix()}，欢迎回来` })

    router
      .push({ path: '/' })
      .catch(() => console.log('登录跳转首页出错,这个错误从哪里来的'))
      .finally(() => {
        loginBtnLoading.value = false
      })
  } catch (error) {
    console.error(error)
    loginBtnLoading.value = false
  }
}
</script>

<style lang="less" scoped>
.loginForm {
  position: relative;
  width: 550px;
  height: 100%;
  background-color: #ffffff;
  box-sizing: border-box;

  .officialWebsiteLink {
    position: absolute;
    top: 16px;
    right: 16px;
    font-size: 20px;
  }

  .formWarp {
    position: absolute;
    top: calc(50% - 180px);
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .title {
      margin-bottom: 30px;
      text-align: left;
      font-size: 18px;
      font-weight: 550;
      line-height: 31px;
      // color: @primary-color;
      opacity: 1;
    }

    .productLogo {
      height: 68px;
      margin-bottom: 40px;
    }

    .form {
      width: 340px;

      :deep(.ant-input-affix-wrapper) {
        height: 50px;
        width: 100%;
        color: #333;
        font-size: 16px;
        font-weight: normal;
        box-shadow: 0px 3px 4px rgba(0, 0, 0, 0.05);
        // border-radius: 4px;
        box-sizing: border-box;
        border: 1px solid #d5d5d5;
        background-color: #fff;

        input {
          &:-webkit-autofill {
            -webkit-box-shadow: 0 0 0 1000px #fff inset;
          }

          &:-internal-autofill-selected {
            transition: background-color 5000s ease-in-out 0s !important;
          }
        }
      }

      .password {
        :deep(.ant-input) {
          letter-spacing: 4px;
        }
      }
    }

    .loginBtn {
      width: 100%;
      height: 48px;
      font-size: 20px;
      padding: 0px 20px;
      color: #fff;

      border-radius: 5px;
      letter-spacing: 2px;
      text-align: center;
    }
  }
}
</style>
