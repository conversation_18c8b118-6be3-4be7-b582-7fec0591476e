<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-15 14:30:59
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-18 13:55:54
 * @FilePath: /global-intelligence-web/src/views/task/components/chat/editor/agentSelect.vue
 * @Description: 多选下拉选择器组件，支持多选和自定义显示
-->
<template>
  <!-- 主容器，点击时打开下拉框 -->
  <div class="agentSelect" :data-key="slotData.id" contenteditable="false" @click.stop="setOpenSelectDropdown(true)">
    <!-- 隐藏的ant-design下拉选择器，用于实际的选择功能 -->
    <a-select
      style="visibility: hidden; width: 0"
      ref="selectRef"
      :dropdownMatchSelectWidth="false"
      :open="openSelectDropdown"
      :mode="props.slotData.props?.mode === 'multiple' ? 'multiple' : undefined"
      @change="handleChange"
      :getPopupContainer="getPopupContainer"
    >
      <a-select-option v-for="item in selectOptions" :key="item" :value="item">{{ item }}</a-select-option>
    </a-select>

    <!-- 自定义显示区域 -->
    <span
      :class="['select-content', isEmpty(props.value) ? 'show-placeholder' : '']"
      :data-placeholder="placeholder"
      ref="inputRef"
      contenteditable="false"
    >
      {{ props.value }}
    </span>
    <!-- 下拉箭头图标 -->
    <DownOutlined :class="['fs-12px ml4px', props.value ? 'color-#000-25' : 'color-#f4f0ff']" />
  </div>
</template>

<script setup lang="ts">
// 导入所需的依赖
import { computed, ref, useTemplateRef } from 'vue'
import type { PromptItemType } from './index.vue'
import { onClickOutside } from '@vueuse/core'
import { isEmpty } from 'lodash-es'
import { DownOutlined } from '@ant-design/icons-vue'

// 类型定义
interface Props {
  value: string | undefined // 输入框的值
  placeholder?: string // 占位符文本
  slotData: PromptItemType // 插槽数据
}

// Props和Emits定义
const props = defineProps<Props>()
const emit = defineEmits<{
  'update:value': [value: string] // v-model双向绑定事件
  change: [value: string] // change事件
  navigate: [direction: 'left' | 'right', slotData: PromptItemType] // 导航事件
}>()

// 下拉框状态管理
const openSelectDropdown = ref(false)
const selectRef = useTemplateRef<HTMLElement>('selectRef')

// 选项处理
const selectOptions = computed(() => {
  return props.slotData.props?.options?.split(',') || []
})

// 下拉框容器设置
const getPopupContainer = () => document.body

// 事件处理函数
function handleChange(val: string | string[]) {
  const tempVal = props.slotData.props?.mode === 'multiple' ? (val as string[]).join('、') : (val as string)
  emit('update:value', tempVal)
  emit('change', tempVal)
  if (props.slotData.props?.mode !== 'multiple') {
    setOpenSelectDropdown(false)
  }
}

function setOpenSelectDropdown(val: boolean) {
  if (openSelectDropdown.value === val) return
  openSelectDropdown.value = val
}

// 点击外部关闭下拉框
onClickOutside(
  selectRef,
  () => {
    setOpenSelectDropdown(false)
  },
  {
    ignore: ['.ant-select-dropdown']
  }
)

// 暴露给父组件的方法和属性
defineExpose({
  selectRef
})
</script>

<style scoped lang="less">
// 定义插槽样式变量
@slotRadius: 8px;
@slotBgColor: #cfc5f2;
@slotMargin: 4px;
@slotPaddingX: 6px;
@slotPaddingY: 2px;
@slotHeight: 26px;

// 输入框容器样式
.agentSelect {
  display: inline-flex;
  align-items: center;
  height: @slotHeight;
  line-height: @slotHeight;
  margin: 0 @slotMargin;
  border-radius: @slotRadius;
  padding: @slotPaddingY @slotPaddingX;
  background: @slotBgColor;
  color: #6553ee;
  cursor: text;
  outline: none;

  // 输入内容区域样式
  .select-content {
    outline: none;
    min-width: 1px;
    color: #6553ee;

    // 只在空内容且有 show-placeholder 类时显示 placeholder
    &.show-placeholder:empty::before {
      content: attr(data-placeholder);
      color: #f4f0ff;
      pointer-events: none;
    }
  }
}

// 辅助元素样式
.widgetBuffer,
.slot-side-right,
.slot-side-left {
  vertical-align: text-top;
  height: @slotHeight;
  width: 0;
  display: inline;
}
</style>
