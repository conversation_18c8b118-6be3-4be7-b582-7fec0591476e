<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-15 14:30:59
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-18 16:05:19
 * @FilePath: /global-intelligence-web/src/views/task/components/chat/editor/agentInput.vue
 * @Description: 可编辑的输入框组件，支持光标位置保持和placeholder显示，用于在编辑器中插入可编辑的文本片段
-->
<template>
  <div class="agentInput" :data-key="slotData.id" contenteditable="false" @keydown.delete="handleDEl">
    <span
      :class="['input-content', isEmpty(props.value) ? 'show-placeholder' : '']"
      :data-placeholder="placeholder"
      ref="inputRef"
      contenteditable="true"
      @blur="handleBlur"
      @input="handleInput"
      @paste="handleInput"
      @cut="handleInput"
    ></span>
    <!-- @paste="handlePaste"
      @cut="handleCut" -->
  </div>
</template>

<script setup lang="ts">
// 导入必要的依赖
import { nextTick, useTemplateRef, watch } from 'vue'
import type { PromptItemType } from './index.vue'
import { isEmpty } from 'lodash-es'

// 类型定义
interface Props {
  value: string | undefined // 输入框的值
  placeholder?: string // 占位符文本
  slotData: PromptItemType // 插槽数据
}

// Props和Emits声明
const props = defineProps<Props>()
const emit = defineEmits<{
  'update:value': [value?: string] // v-model双向绑定事件
  input: [value: string | undefined, slotData: PromptItemType] // 输入事件
  blur: [slotData: PromptItemType] // 失去焦点事件
  navigate: [direction: 'left' | 'right', slotData: PromptItemType] // 导航事件
  delete: [event: KeyboardEvent] // 删除事件
}>()

// DOM引用
const inputRef = useTemplateRef('inputRef')

// 组件对外暴露的属性和方法
defineExpose({
  inputRef
})

// 监听值变化
watch(
  () => props.value,
  newVal => {
    console.log('值发生变化: ', newVal)
    if (inputRef.value) {
      // 保存当前光标位置
      const selection = window.getSelection()
      const range = selection?.getRangeAt(0)
      const startOffset = range?.startOffset
      const endOffset = range?.endOffset

      inputRef.value.innerHTML = newVal || ''

      // 恢复光标位置
      if (selection && range && startOffset !== undefined && endOffset !== undefined) {
        range.setStart(inputRef.value.firstChild || inputRef.value, startOffset)
        range.setEnd(inputRef.value.firstChild || inputRef.value, endOffset)
        selection.removeAllRanges()
        selection.addRange(range)
      }
    }
  }
)

// 事件处理函数
/**
 * 处理删除键按下事件
 * 当内容为空时触发删除事件
 */
function handleDEl(e: KeyboardEvent) {
  e.stopPropagation()
  if (isEmpty(props.value)) {
    emit('delete', e)
  }
}

/**
 * 处理输入事件
 * 处理输入内容的格式化和更新
 */
function handleInput(e: Event) {
  e.stopPropagation()
  const value = inputRef.value?.innerHTML
  // 处理特殊情况：当只有一个br标签时视为空内容
  let textValue = value === '<br>' ? '' : inputRef.value?.innerText.trim() || ''
  textValue = textValue.replace(/\n/g, '').replace(/ /g, '')

  // 清理空内容
  if (isEmpty(textValue) && inputRef.value) {
    inputRef.value.innerHTML = ''
  }

  console.log('textValue: ', textValue)
  // 触发更新事件
  emit('update:value', textValue)
  emit('input', textValue, props.slotData)
}

/**
 * 处理失去焦点事件
 * 清理空内容并触发相关事件
 */
function handleBlur() {
  if (inputRef.value) {
    const value = inputRef.value.innerText || ''
    // 检查并清理空内容
    if (value.trim() === '' || value === '\n') {
      inputRef.value.innerHTML = ''
      emit('update:value', '')
      emit('input', '', props.slotData)
    }
  }
  emit('blur', props.slotData)
}

/**
 * 处理粘贴事件
 * 粘贴后需要更新值
 */
function handlePaste(e: ClipboardEvent) {
  e.stopPropagation()
  // 使用 setTimeout 确保粘贴内容已经插入到DOM中
  nextTick(() => {
    const value = inputRef.value?.innerHTML
    // 处理特殊情况：当只有一个br标签时视为空内容
    const textValue = value === '<br>' ? '' : inputRef.value?.innerText.trim() || ''

    // 清理空内容
    if (isEmpty(textValue) && inputRef.value) {
      inputRef.value.innerHTML = ''
    }

    console.log('paste textValue: ', textValue)
    // 触发更新事件
    emit('update:value', textValue)
    emit('input', textValue, props.slotData)
  })
}

/**
 * 处理剪切事件
 * 剪切后需要更新值
 */
function handleCut(e: ClipboardEvent) {
  e.stopPropagation()
  // 使用 setTimeout 确保剪切操作已经完成
  nextTick(() => {
    const value = inputRef.value?.innerHTML
    // 处理特殊情况：当只有一个br标签时视为空内容
    const textValue = value === '<br>' ? '' : inputRef.value?.innerText.trim() || ''

    // 清理空内容
    if (isEmpty(textValue) && inputRef.value) {
      inputRef.value.innerHTML = ''
    }

    console.log('cut textValue: ', textValue)
    // 触发更新事件
    emit('update:value', textValue)
    emit('input', textValue, props.slotData)
  })
}
</script>

<style scoped lang="less">
// 基础样式变量定义
@slotRadius: 8px;
@slotBgColor: #cfc5f2;
@slotMargin: 4px;
@slotPaddingX: 6px;
@slotPaddingY: 2px;
@slotHeight: 26px;
@slotColor: #f4f0ff;
@textColor: #6553ee;

// 输入框容器样式
.agentInput {
  display: inline-flex;
  align-items: center;
  min-height: @slotHeight;
  line-height: @slotHeight;
  margin: 0 @slotMargin;
  border-radius: @slotRadius;
  padding: @slotPaddingY @slotPaddingX;
  background: @slotBgColor;
  color: @textColor;
  cursor: text;
  outline: none;
  flex-wrap: wrap;

  // 输入内容区域样式
  .input-content {
    outline: none;
    min-width: 1px;
    color: @textColor;

    // 占位符样式
    &.show-placeholder:empty::before {
      content: attr(data-placeholder);
      color: @slotColor;
      pointer-events: none;
    }
  }
}

// 通用内容样式
.placeholder,
.slot-content {
  color: @slotColor;
  background-color: @slotBgColor;
  word-break: break-all;
  padding: 2px 0;
  line-height: 20px;
}

// 辅助元素样式
.widgetBuffer {
  vertical-align: middle;
  height: 100%;
  width: 0;
}
</style>
