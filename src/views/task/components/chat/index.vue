<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-14 10:36:13
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-21 16:50:49
 * @FilePath: /global-intelligence-web/src/views/task/components/chat/index.vue
 * @Description: 
-->
<template>
  <div class="chatBox flex flex-direction-column">
    <!-- 消息列表 -->
    <div ref="msgListRef" class="msgList flex flex-direction-column items-center flex-1 overflow-auto pt16px">
      <a-spin :spinning="historyLoading" class="h-full flex-center-center" size="large">
        <template v-for="(item, index) in msgList" :key="index">
          <AgentMsgItem v-if="item.role === 'agent'" :item="item" class="max-w-800px w800px" />
          <UserMsgItem v-else-if="item.role === 'user'" :item="item" class="max-w-800px w800px" />
        </template>
      </a-spin>
    </div>

    <!-- 输入框 -->
    <div class="border-[0.5px] w-full p-16px box-shadow relative rounded-2xl max-w-800px w800px mx-auto">
      <Editor
        v-model:value="editorValue"
        :agentPrompt="agentPrompt"
        v-model:sendBtnDisabled="sendBtnDisabled"
        @enter="handleSendClick"
      />

      <div class="relative flex-end-center">
        <template v-if="sendBtnDisabled">
          <a-tooltip>
            <template #title>请先填写任务必要信息</template>
            <div class="bg-#ccc inline-flex rounded-50% w30px h30px flex-center-center">
              <iconfontIcon icon="icon-arrow-up" class="color-#fff fw-bold fs-24px" />
            </div>
          </a-tooltip>
        </template>
        <template v-else>
          <div
            class="bg-#6553ee hover:(bg-#baabff) inline-flex rounded-50% w30px h30px flex-center-center cursor-pointer transition-all"
            @click="handleSendClick"
          >
            <iconfontIcon icon="icon-arrow-up" class="color-#fff fw-bold fs-24px" />
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch, nextTick } from 'vue'
import Editor from './editor/index.vue'
import { useRoute } from 'vue-router'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { agentChatMessagesStop, agentMessages } from '@/api/api'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { useUserStore } from '@/store'
import { has, isEmpty, uniq } from 'lodash-es'
import UserMsgItem, { type UserMsgItemType } from './msgItem/userMsgItem.vue'
import AgentMsgItem, { type AgentMsgItemType } from './msgItem/agentMsgItem.vue'
import { randomUUID } from '@/utils/util'
import type { AgentMsgStepType } from './msgItem/agentMsgStep.vue'

const editorValue = ref('')
const userStore = useUserStore()
const route = useRoute()
const agentId = route.query.taskExampleId as string

const agentList = [
  {
    name: '纯输入框',
    id: '1',
    query:
      '我公司是' +
      '{"type":"InputSlot","props":{"placeholder":"公司介绍"}}' +
      '，我负责销售' +
      '{"type":"InputSlot","props":{"placeholder":"产品、服务或解决方案"}}' +
      '，正在寻找' +
      '{"type":"InputSlot","props":{"placeholder":"国家"}}' +
      '和' +
      '{"type":"InputSlot","props":{"placeholder":"行业"}}' +
      '的机会，请根据情报数据网帮我洞察机会点，并对机会的价值进行评级。'
  },
  {
    name: '多选框',
    id: '2',
    query:
      '正在寻找' +
      '{"type":"SelectSlot","props":{"placeholder":"国家(单选)","options":"中国,美国,日本","mode":"single"}}' +
      '和' +
      '{"type":"SelectSlot","props":{"placeholder":"行业(多选)","options":"服装,美妆,彩妆,电子,IT","mode":"multiple"}}' +
      '的机会，请根据情报数据网帮我洞察机会点，并对机会的价值进行评级。'
  },
  {
    name: '组合输入',
    id: '3',
    query:
      '我公司是' +
      '{"type":"InputSlot","props":{"placeholder":"公司介绍"}}' +
      '，我负责销售' +
      '{"type":"InputSlot","props":{"placeholder":"产品、服务或解决方案"}}' +
      '，正在寻找' +
      '{"type":"SelectSlot","props":{"placeholder":"国家(单选)","options":"中国,美国,日本","mode":"single"}}' +
      '和' +
      '{"type":"SelectSlot","props":{"placeholder":"行业(多选)","options":"服装,美妆,彩妆,电子,IT","mode":"multiple"}}' +
      '的机会，请根据情报数据网帮我洞察机会点，并对机会的价值进行评级。'
  }
]

const agentPrompt = computed(() => {
  const agent = agentList.find(item => item.id === agentId)
  return agent ? agent.query : ''
})

// 获取历史消息
// const conversationId = computed(() => () || '')
const historyLoading = ref(false)
async function getHistoryMessages() {
  try {
    historyLoading.value = true
    const { result } = await agentMessages({
      agentType: 'GIS_AGENT_OPPORTUNITY_POINT',
      conversationId: route.query.conversationId as string,
      limit: 20
    })
    result.data.map(item => {
      const stepsList: AgentMsgStepType[] = []
      item.answerProcess
        .filter(
          processItem =>
            !['workflow_started', 'workflow_finished', 'message_end', 'message'].includes(processItem.event)
        )
        .forEach(processItem => {
          const msgData = handleSSEData(processItem)

          const index = stepsList.findIndex(item => item.id === msgData.id)
          // 判断历史消息内有没有相同id的，有就更新，没有就插入
          if (index === -1) {
            stepsList.push(msgData)
          } else {
            stepsList[index] = {
              ...stepsList[index],
              status: msgData.status || 'loading',
              title: msgData.title,
              rawData: item
            }
          }
        })
      // 先插入answer
      msgList.value.unshift({
        id: randomUUID(),
        role: 'agent',
        content: {
          steps: stepsList,
          reply: item.answer
        },
        timestamp: new Date().toLocaleString()
      })

      // 再插入query
      msgList.value.unshift({
        id: randomUUID(),
        role: 'user',
        content: item.query,
        timestamp: new Date().toLocaleString()
      })
    })
    historyLoading.value = false
  } catch (error) {
    historyLoading.value = false
    console.error('获取历史消息失败:', error)
  }
}

const sendBtnDisabled = ref(true)
const sendLoading = ref(false)
const msgList = ref<(AgentMsgItemType | UserMsgItemType)[]>([])
const msgListRef = ref<HTMLElement | null>(null)

// 滚动到底部方法
const scrollToBottom = () => {
  nextTick(() => {
    if (msgListRef.value) {
      msgListRef.value.scrollTop = msgListRef.value.scrollHeight
    }
  })
}

function handleSendClick() {
  // try {
  // console.log('handleSendClick e: ', e)
  sendLoading.value = true
  // 插入user信息
  msgList.value.push({
    id: randomUUID(),
    role: 'user',
    content: editorValue.value,
    timestamp: new Date().toLocaleString()
  })
  // 滚动到底部
  scrollToBottom()
  const params = {
    agentType: 'GIS_AGENT_OPPORTUNITY_POINT',
    query: editorValue.value
  }
  editorValue.value = ''

  const token = userStore.token
  fetchEventSource(`${import.meta.env.VITE_BASE_API}/agent/chat-messages`, {
    // fetchEventSource(`http://localhost:3005/agent/chat-messages`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-access-token': token!
    },
    body: JSON.stringify(params),
    openWhenHidden: true,
    async onopen(response) {
      // 连接打开时的回调
      console.log('response: ', response)
      console.log('Connection opened:', response.status)
      // 插入agent信息
      msgList.value.push({
        id: '',
        role: 'agent',
        content: {
          steps: [],
          reply: ''
        },
        timestamp: new Date().toLocaleString()
      })
      // 滚动到底部
      scrollToBottom()
    },
    onmessage(event) {
      // 处理接收到的消息
      if (isEmpty(event.data)) {
        return
      }
      const data = JSON.parse(event.data)

      const agentMstItem = msgList.value[msgList.value.length - 1] as AgentMsgItemType

      // 设置id
      if (isEmpty(agentMstItem.id)) {
        agentMstItem.id = data.message_id
      }

      // 过滤不要的类型
      if (['workflow_started', 'message_end', 'message'].includes(data.event)) {
        return
      }
      const msgData = handleSSEData(data)
      if (data.event === 'workflow_finished') {
        agentMstItem.content.reply = msgData.title
      } else {
        const index = agentMstItem.content.steps.findIndex(item => item.id === msgData.id)
        // 判断历史消息内有没有相同id的，有就更新，没有就插入
        if (index === -1) {
          agentMstItem.content.steps.push(msgData)
        } else {
          agentMstItem.content.steps[index] = {
            ...agentMstItem.content.steps[index],
            status: msgData.status || 'loading',
            title: msgData.title,
            rawData: data
          }
        }
      }
      // 滚动到底部
      scrollToBottom()
    },

    onerror(error) {
      // 错误处理
      console.error('Error:', error)
      throw new Error(error)
    },
    onclose() {
      // 连接关闭时的回调
      console.log('Connection closed', msgList.value)
    }
  })
  // } catch (error) {
  //   console.error('处理SSE数据时出错:', error)
  // }
}

// 对sse的数据进行处理
function handleSSEData(data: any) {
  const msgData: AgentMsgStepType = {
    event: data.event,
    id: data.data.id,
    index: data.data.index,
    status: data.data.status || 'loading',
    node_id: data.data.node_id,
    node_type: data.data.node_type,
    loop_id: data.data.loop_id,
    title: '',
    rawData: data
  }
  // 设置展示的标题
  if (data.event === 'workflow_started') {
    msgData.title = data.data.inputs['sys.query']
    // agentMstItem.content.steps.push(msgData)
  } else if (data.event === 'workflow_finished') {
    msgData.title = data.data.outputs.answer
    // agentMstItem.content.reply = data.data.outputs.answer
  } else {
    msgData.title = data.data.title
  }

  return msgData
}

onMounted(() => {
  if (!isEmpty(route.query.conversationId)) {
    getHistoryMessages().then(() => {
      // 加载历史消息后滚动到底部
      scrollToBottom()
    })
  }
})
</script>

<style scoped></style>
