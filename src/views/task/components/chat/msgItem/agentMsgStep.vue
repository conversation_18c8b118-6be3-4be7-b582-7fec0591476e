<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-21 09:57:28
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-21 11:20:16
 * @FilePath: /global-intelligence-web/src/views/task/components/chat/msgItem/agentMsgStep.vue
 * @Description: 
-->
<template>
  <div class="my8px">
    <div class="inline-flex items-start rounded-8px p8px bg-#f5f5f5 color-#000-75">
      <div class="h100%">
        <iconfontIcon :icon="icon" class="lh-24px"></iconfontIcon>
      </div>
      <span class="ml-4px">{{ item.title }}</span>
    </div>
    <a-spin class="ml-8px" size="small" v-if="item.status === 'loading'"></a-spin>
  </div>
</template>

<script setup lang="ts">
import iconfontIcon from '@/components/tools/iconfontIcon'
import { computed } from 'vue'

export interface AgentMsgStepType {
  event: string
  id: string
  index: string
  status: string
  node_id: string
  node_type: string
  loop_id: string
  title: string
  rawData: Record<string, any>
}
const props = defineProps<{ item: AgentMsgStepType }>()

const icon = computed(() => {
  let iconName = ''
  switch (props.item.node_type) {
    case 'llm':
      iconName = 'icon-internet'
      break
    case 'code':
      iconName = 'icon-search'
      break
    case 'tool':
      iconName = 'icon-search'
      break
    case 'loop':
      iconName = 'icon-leidatance'
      break
    case 'if-else':
      iconName = 'icon-filter2'
      break
    default:
      iconName = 'icon-a-controlplatform'
      break
  }

  return iconName
  //   llm  》分析思考
  // code 》查询
  // tool 》 工具
  // loop 》 分析
  // if-else 》 分析
})
</script>

<style scoped></style>
