<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-14 10:34:09
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-21 16:07:18
 * @FilePath: /global-intelligence-web/src/views/task/components/group.vue
 * @Description: 
-->
<template>
  <div class="h-full flex flex-direction-column">
    <div class="p16px">
      <a-button
        block
        type="primary"
        @click="
          router.push({
            path: '/task',
            query: { timestamp: new Date().valueOf() }
          })
        "
      >
        <div class="flex-center-center">
          <iconfontIcon icon="icon-add" />
          <span> 新任务 </span>
        </div>
      </a-button>
    </div>

    <p class="fs-14px color-#666 px16px mb8px">历史对话</p>
    <a-spin :spinning="loading">
      <ul class="overflow-auto flex-1 px16px pb16px">
        <li
          :class="[
            'groupItem fs-16px color-[#000-88] br-6px cursor-pointer hover:(bg-#ebebeb) ellipsis',
            currentConversationId === item.id ? 'bg-#f4f0ff' : ''
          ]"
          v-for="(item, index) in dataList"
          :key="index"
          @click="handleClick(item)"
        >
          <p class="transition-all ellipsis px-8px py-6px">
            <iconfontIcon icon="icon-chat"></iconfontIcon>
            {{ item.name }}
          </p>
        </li>

        <div v-if="dataList.length > 0" class="loadMore" v-intersection-observer="handlerIntersectionObserver">
          <p class="endText" v-if="!noMore"><a-spin tip="加载中..."></a-spin></p>
          <p class="endText" v-else>没有更多了</p>
        </div>
      </ul>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { agentConversations } from '@/api/api'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { ChatBotConversationsDto } from '~/types/api/agent/conversations'
import { vIntersectionObserver } from '@vueuse/components'

const router = useRouter()
const route = useRoute()
const currentConversationId = computed(() => route.query.conversationId as string)

const dataList = ref<ChatBotConversationsDto[]>([])
const loading = ref(false)
const noMore = ref(false)

async function getData() {
  loading.value = true
  try {
    const { result } = await agentConversations({
      agentType: 'GIS_AGENT_OPPORTUNITY_POINT',
      limit: 100,
      lastId: dataList.value.length ? dataList.value[dataList.value.length - 1].id : undefined
    })
    dataList.value = dataList.value.concat(result.data || [])
    noMore.value = !result.hasMore
    loading.value = false
  } catch (error) {
    loading.value = false
    console.error(error)
  }
}

function handlerIntersectionObserver(intersectionObserverList: IntersectionObserverEntry[]) {
  const { isIntersecting } = intersectionObserverList[0]
  if (isIntersecting && !loading.value && !noMore.value) {
    getData()
  }
}

function handleClick(item: ChatBotConversationsDto) {
  router.push(`/task?conversationId=${item.id}&timestamp=${new Date().valueOf()}`)
}

onMounted(() => {
  getData()
})
</script>

<style lang="less" scoped>
.groupItem {
  + .groupItem {
    margin-top: 8px;
  }
}
</style>
