<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-14 10:20:39
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-21 16:06:11
 * @FilePath: /global-intelligence-web/src/views/task/index.vue
 * @Description: 
-->
<template>
  <div class="flex h-full">
    <div class="min-w-260px max-w-260px h100% border-c-#050505-6 border-r-1px border-solid">
      <Group />
    </div>
    <div class="flex-1 overflow-hidden flex flex-direction-column items-center pb16px">
      <ChatIndex :key="timestamp" :class="['mx-auto w-full', hideAgentTips ? 'h-full' : 'mt35vh']" />

      <div v-if="!hideAgentTips" class="max-w-800px w800px mx-auto mt32px grid grid-cols-3 gap-16px">
        <a-card
          v-for="(item, index) in agentList"
          :key="index"
          class="hover:(shadow-sm) transition-all duration-300 relative group rounded-lg"
          size="small"
        >
          <div class="mb8px fs-14px color-#000-90">{{ item.name }}</div>
          <div class="h160px overflow-hidden rounded-lg">
            <img src="https://www.bengine.com.cn/images/home/<USER>" alt="" class="w-full h-full object-cover" />
            <div
              class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center rounded-lg"
            >
              <a-button
                type="primary"
                class="scale-0 group-hover:scale-100 transition-all"
                @click="handleCopyAgent(item)"
              >
                做同款
              </a-button>
            </div>
          </div>
        </a-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Group from './components/group.vue'
import ChatIndex from './components/chat/index.vue'
import { useRoute, useRouter } from 'vue-router'
import { computed, watch } from 'vue'
import { isEmpty } from 'lodash-es'

const route = useRoute()

const conversationId = computed(() => (route.query.conversationId as string) || '')
const timestamp = computed(() => (route.query.timestamp as string) || '')
// const taskExampleId = computed(() => (route.query.taskExampleId as string) || '')
const hideAgentTips = computed(() => !isEmpty(conversationId.value))

const agentList = [
  {
    name: '纯输入框',
    id: '1',
    query:
      '我公司是' +
      '{"type":"InputSlot","props":{"placeholder":"公司介绍"}}' +
      '，我负责销售' +
      '{"type":"InputSlot","props":{"placeholder":"产品、服务或解决方案"}}' +
      '，正在寻找' +
      '{"type":"InputSlot","props":{"placeholder":"国家"}}' +
      '和' +
      '{"type":"InputSlot","props":{"placeholder":"行业"}}' +
      '的机会，请根据情报数据网帮我洞察机会点，并对机会的价值进行评级。'
  },
  {
    name: '多选框',
    id: '2',
    query:
      '正在寻找' +
      '{"type":"SelectSlot","props":{"placeholder":"国家(单选)","options":"中国,美国,日本","mode":"single"}}' +
      '和' +
      '{"type":"SelectSlot","props":{"placeholder":"行业(多选)","options":"服装,美妆,彩妆,电子,IT","mode":"multiple"}}' +
      '的机会，请根据情报数据网帮我洞察机会点，并对机会的价值进行评级。'
  },
  {
    name: '组合输入',
    id: '3',
    query:
      '我公司是' +
      '{"type":"InputSlot","props":{"placeholder":"公司介绍"}}' +
      '，我负责销售' +
      '{"type":"InputSlot","props":{"placeholder":"产品、服务或解决方案"}}' +
      '，正在寻找' +
      '{"type":"SelectSlot","props":{"placeholder":"国家(单选)","options":"中国,美国,日本","mode":"single"}}' +
      '和' +
      '{"type":"SelectSlot","props":{"placeholder":"行业(多选)","options":"服装,美妆,彩妆","mode":"multiple"}}' +
      '的机会，请根据情报数据网帮我洞察机会点，并对机会的价值进行评级。'
  }
]

const router = useRouter()
function handleCopyAgent(item: any) {
  console.log(item)
  router.replace(`/task?taskExampleId=${encodeURIComponent(item.id)}&timestamp=${new Date().getTime()}`)
  // `agentPrompt=${encodeURIComponent(item.query)}`
  // `agentName=${encodeURIComponent(item.name)}`
}
</script>

<style scoped></style>
