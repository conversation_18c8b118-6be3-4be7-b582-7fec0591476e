<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-02-26 15:59:07
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-27 14:08:19
 * @FilePath: /global-intelligence-web/src/views/intelligence/components/intelligenceList.vue
 * @Description: 
-->
<template>
  <div class="h100% flex flex-direction-column">
    <div class="titleBox h62px p-16px border-c-#050505-6 border-b-1px border-solid">
      <!-- <p class="fw-550 color-#000-88 fs-20">情报</p> -->
      <div class="extra h100% text-right">
        <a-space>
          <a-tooltip v-if="!['全部', '我的订阅'].includes(activityGroupId)">
            <template #title>阅读全部</template>
            <iconfontIcon
              icon="icon-clear"
              :extra-common-props="{ class: ['fs-22px hoverPrimaryColor'] }"
              @click="handleReadAll"
            />
          </a-tooltip>

          <a-popover
            placement="bottomLeft"
            trigger="click"
            :overlayInnerStyle="{ padding: '0px' }"
            v-model:open="filterPopoverVisible"
            @openChange="handleFilterPopoverVisibleChange"
          >
            <template #content>
              <a-card :bordered="false">
                <div class="w-400px">
                  <a-form :model="formData" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" autocomplete="off">
                    <a-form-item label="关键字" name="keyword">
                      <a-input
                        v-model:value="formData.keyword"
                        placeholder="多个关键字使用逗号分割"
                        @pressEnter="handleFilterSubmit"
                        v-encomma
                      />
                    </a-form-item>
                    <a-form-item label="行业" name="industryLabelIdList">
                      <a-select
                        mode="multiple"
                        :options="industryOptions"
                        placeholder="行业"
                        v-model:value="formData.industryLabelIdList"
                        style="width: 100%"
                        show-search
                        :fieldNames="{ label: 'labelName', value: 'id' }"
                        :filter-option="(input: string, option: any) => option.labelName.toLowerCase().indexOf(input.toLowerCase()) >= 0"
                      />
                    </a-form-item>
                    <a-form-item label="事件类型" name="eventLabelIdList">
                      <a-select
                        mode="multiple"
                        :options="eventOptions"
                        placeholder="事件类型"
                        v-model:value="formData.eventLabelIdList"
                        style="width: 100%"
                        show-search
                        :fieldNames="{ label: 'labelName', value: 'id' }"
                        :filter-option="(input: string, option: any) => option.labelName.toLowerCase().indexOf(input.toLowerCase()) >= 0"
                      />
                    </a-form-item>
                    <a-form-item label="国家" name="continentCountry">
                      <a-tree-select
                        v-model:value="formData.countryLabelIdList"
                        style="width: 100%"
                        :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                        placeholder="国家"
                        :tree-data="continentCountryOptions"
                        :fieldNames="{ label: 'labelName', value: 'id', children: 'subList' }"
                        treeCheckable
                        :showCheckedStrategy="TreeSelect.SHOW_CHILD"
                        :maxTagCount="10"
                        tree-node-filter-prop="labelName"
                      />
                    </a-form-item>
                    <a-form-item label="品牌" name="brandLabelIdList">
                      <a-select
                        mode="multiple"
                        :options="brandOptions"
                        placeholder="品牌"
                        v-model:value="formData.brandLabelIdList"
                        style="width: 100%"
                        show-search
                        :fieldNames="{ label: 'labelName', value: 'id' }"
                        :filter-option="(input: string, option: any) => option.labelName.toLowerCase().indexOf(input.toLowerCase()) >= 0"
                      />
                    </a-form-item>
                  </a-form>
                </div>
                <template #actions>
                  <div class="flex-between-center px16px">
                    <div>
                      <a-button type="link" @click="handleFilterReset">重置</a-button>
                    </div>
                    <div class="flex-1 text-right">
                      <a-space>
                        <a-button @click="filterPopoverVisible = false">取消</a-button>
                        <a-button type="primary" @click="handleFilterSubmit"> 筛选</a-button>
                      </a-space>
                    </div>
                  </div>
                </template>
              </a-card>
            </template>
            <a-badge :dot="isHaveSearchCondition">
              <iconfontIcon icon="icon-filter2" :extra-common-props="{ class: ['fs-22px hoverPrimaryColor'] }" />
            </a-badge>
          </a-popover>
        </a-space>
      </div>
    </div>

    <template v-if="intelligenceList.length > 0">
      <a-list
        :loading="dataListLoading"
        item-layout="vertical"
        :data-source="intelligenceList"
        class="flex-1 overflow-auto scrollbar-none scrollbar-w-4px"
      >
        <template #renderItem="{ item, index }: { item: newsPageResType, index: number }">
          <div
            :class="[
              'hover:bg-#ebebeb p-16px cursor-pointer lh-1.6em',
              !item.isRead ? 'tipsPoint' : '',
              detailCurrentData?.dataId === item.dataId ? 'bg-#EAE7FB-50' : 'bg-#fff'
            ]"
            @click="handleNewsCardClick(item)"
          >
            <div class="flex-start-center color-#000-45">
              <span v-if="item.websiteName" class="mr-4px white-space-nowrap">
                {{ item.websiteName }}
              </span>
              <span v-if="item.websiteName !== item.author" class="mr-4px ellipsis">
                {{ item.author }}
              </span>
              <span class="flex-1 white-space-nowrap">
                {{ dayjs(item.publishTime).format('YYYY-MM-DD') }}
              </span>
            </div>

            <p class="fw-550 color-#000-88 ellipsis-2 fs-16 m0 mt-4px">
              {{ item.translateTitle }}
            </p>

            <div class="ellipsis-2 color-#000-65 mt-4px">
              {{ clearHtmlTag(contentText(item.translateContent)) }}
            </div>

            <div class="overflow-hidden ellipsisTag mt-4px">
              <div class="tagContent">
                <a-tag
                  v-for="(tag, index) in item.tagMap['INDUSTRY_AFTER']"
                  :key="index"
                  color="#6553ee"
                  :bordered="false"
                  class="mb-8px"
                >
                  {{ tag }}
                </a-tag>
                <a-tag
                  v-for="(tag, index) in item.tagMap['EVENT']"
                  :key="index"
                  color="#D8F4F8"
                  :bordered="false"
                  class="mb-8px color-#595959!"
                >
                  {{ tag }}
                </a-tag>
                <a-tag
                  v-for="(tag, index) in item.tagMap['CONTINENT']"
                  :key="index"
                  color="#FCEBE0"
                  :bordered="false"
                  class="mb-8px color-#595959!"
                >
                  {{ tag }}
                </a-tag>
                <a-tag
                  v-for="(tag, index) in item.tagMap['COUNTRY']"
                  :key="index"
                  color="#FCEBE0"
                  :bordered="false"
                  class="mb-8px color-#595959!"
                >
                  {{ tag }}
                </a-tag>
                <a-tag
                  v-for="(tag, index) in item.tagMap['BRAND_AFTER']"
                  :key="index"
                  color="#D6E5FD"
                  :bordered="false"
                  class="mb-8px color-#595959!"
                >
                  {{ tag }}
                </a-tag>
                <a-tag
                  v-for="(tag, index) in item.tagMap['PRODUCT_TYPE']"
                  :key="index"
                  :bordered="false"
                  class="mb-8px color-#595959!"
                >
                  {{ tag }}
                </a-tag>
              </div>
            </div>
          </div>
          <div v-if="index !== intelligenceList.length - 1" class="h1px bg-#f3f3f3" />
        </template>
        <template #loadMore>
          <div
            v-if="intelligenceList.length > 0"
            class="loadMore py-8px"
            v-intersection-observer="handlerIntersectionObserver"
          >
            <p class="endText" v-if="!noMore"><a-spin tip="加载中..."></a-spin></p>
            <p class="endText" v-else>没有更多了</p>
          </div>
        </template>
      </a-list>
    </template>
    <template v-else>
      <div class="flex-1 flex-center-center">
        <a-spin :spinning="dataListLoading">
          <a-empty :image="Empty.PRESENTED_IMAGE_SIMPLE"></a-empty>
        </a-spin>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, toRef, watch, type Ref } from 'vue'
import type { newsPageReqType, newsPageResType } from '~/types/api/news/page'
import { vIntersectionObserver } from '@vueuse/components'
import useInfiniteLoading from '@/hooks/useInfiniteLoading'
import { getSystemLabel, newsPage, newsRead, newsReadAllTopicNews } from '@/api/api'
import { cloneDeep, isEmpty } from 'lodash-es'
import useRequest from '@/hooks/useRequest'
import type { getSystemLabelReqType } from '~/types/api/system/getSystemLabel'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { useVModel } from '@vueuse/core'
import { Empty, message, TreeSelect } from 'ant-design-vue'
import dayjs from 'dayjs'
import type { NewsTopicSubscribeTopicResType } from '~/types/api/newsTopic/subscribe'
import { useTagStore } from '@/store'
import MarkdownIt from 'markdown-it'

const { getTag } = useTagStore()
const emit = defineEmits(['update:detailCurrentData', 'update:loading', 'readAll', 'read'])
const props = defineProps<{
  activityGroupId: string // 专题id
  activeGroupInfo?: NewsTopicSubscribeTopicResType // 专题信息
  // currentId: string // 当前选中的新闻id
  detailCurrentData: newsPageResType | null // 要显示的新闻详情
  loading: boolean // 控制新闻显示loading
}>()

const activityGroupId = toRef(props, 'activityGroupId')
const loading = useVModel(props, 'loading', emit)
const detailCurrentData = useVModel(props, 'detailCurrentData', emit)

/**
 * @description: 表单筛选相关
 * @param {*} topicId 专题id
 */
function useLabelOptions(topicId: Ref<string>) {
  const getLabelMapParams = computed<getSystemLabelReqType>(() => {
    const params: getSystemLabelReqType = { sceneType: 'news' }
    if (!['全部', '我的订阅'].includes(topicId.value)) {
      params.topicId = topicId.value
    }
    return params
  })
  const { dataList: labelMap, getData: getLabelMap } = useRequest(getSystemLabel, getLabelMapParams)
  const industryOptions = computed(() => {
    if (!['全部', '我的订阅'].includes(topicId.value) && labelMap.value?.industry.length === 0) {
      return getTag('industry')
    }
    return labelMap.value?.industry
  })
  const eventOptions = computed(() => {
    if (!['全部', '我的订阅'].includes(topicId.value) && labelMap.value?.event.length === 0) {
      return getTag('event')
    }
    return labelMap.value?.event
  })
  const continentCountryOptions = computed(() => {
    if (!['全部', '我的订阅'].includes(topicId.value) && labelMap.value?.continent_country.length === 0) {
      return getTag('continent_country')
    }
    return labelMap.value?.continent_country
  })
  const brandOptions = computed(() => {
    if (!['全部', '我的订阅'].includes(topicId.value) && labelMap.value?.brand.length === 0) {
      return getTag('brand')
    }
    return labelMap.value?.brand
  })

  return {
    industryOptions,
    eventOptions,
    continentCountryOptions,
    brandOptions,
    getLabelMap
  }
}

interface FormDataType {
  keyword: string
  /** 行业标签 */
  industryLabelIdList: string[]
  /** 事件标签 */
  eventLabelIdList: string[]
  /** 国家标签 */
  countryLabelIdList: string[]
  /** 洲标签 */
  continentIdList: string[]
  /** 品牌标签 */
  brandLabelIdList: string[]
  /** 专题ID */
  topicId?: string
}
/**
 * @description: 表单下拉筛选项相关
 */
function useFilter() {
  const formData = ref<FormDataType>({
    keyword: '',
    industryLabelIdList: [],
    eventLabelIdList: [],
    countryLabelIdList: [],
    continentIdList: [],
    brandLabelIdList: []
  })
  const filterPopoverVisible = ref(false)

  const formDataReset = () => {
    formData.value = {
      keyword: '',
      industryLabelIdList: [],
      eventLabelIdList: [],
      continentIdList: [],
      countryLabelIdList: [],
      brandLabelIdList: []
    }
  }

  function handleFilterPopoverVisibleChange(visible: boolean) {
    filterPopoverVisible.value = visible
  }

  const isHaveSearchCondition = computed(() => {
    return (
      !isEmpty(formData.value.keyword) ||
      !isEmpty(formData.value.industryLabelIdList) ||
      !isEmpty(formData.value.eventLabelIdList) ||
      !isEmpty(formData.value.countryLabelIdList)
    )
  })

  return {
    formData,
    filterPopoverVisible,
    isHaveSearchCondition,
    formDataReset,
    handleFilterPopoverVisibleChange
  }
}

/**
 * @description: 列表数据
 * @param {*} topicId 专题id
 * @param {*} formData 表单数据
 */
function useListData(topicId: Ref<string>, formData: Ref<FormDataType>) {
  // 请求参数
  const pageReqParams = computed<newsPageReqType>(() => {
    console.log('formData.value: ', cloneDeep(formData.value))
    const params: newsPageReqType = {
      ...formData.value,
      allTopic: topicId.value === '我的订阅'
    }
    if (!['全部', '我的订阅'].includes(topicId.value)) {
      params.topicId = topicId.value
    }
    return params
  })

  const {
    dataList,
    loading: dataListLoading,
    onLoadMore,
    noMore,
    refresh
  } = useInfiniteLoading(newsPage, pageReqParams, {
    immediateReqData: false
  })

  /**
   * @description: 滚动到界面底部回调方法
   * @param {*} intersectionObserverList
   * @return {*}
   */
  function handlerIntersectionObserver(intersectionObserverList: IntersectionObserverEntry[]) {
    const { isIntersecting } = intersectionObserverList[0]
    if (isIntersecting && !dataListLoading.value && !noMore.value) {
      onLoadMore()
    }
  }

  return {
    dataListLoading,
    noMore,
    intelligenceList: dataList,
    refresh,
    handlerIntersectionObserver
  }
}

// 使用自定义hooks
const { formData, filterPopoverVisible, isHaveSearchCondition, formDataReset, handleFilterPopoverVisibleChange } =
  useFilter()
const { industryOptions, eventOptions, continentCountryOptions, brandOptions, getLabelMap } =
  useLabelOptions(activityGroupId)
const { dataListLoading, noMore, refresh, intelligenceList, handlerIntersectionObserver } = useListData(
  activityGroupId,
  formData
)

/**
 * @description: 筛选提交
 * @return {*}
 */
async function handleFilterSubmit() {
  handleFilterPopoverVisibleChange(false)
  refreshPageData()
}

/**
 * @description: 重置筛选
 * @return {*}
 */
function handleFilterReset() {
  formDataReset()
}

/**
 * @description: 点击新闻卡片
 * @param {*} item
 * @return {*}
 */
function handleNewsCardClick(item?: newsPageResType) {
  console.log('item: ', item)
  if (isEmpty(item)) {
    detailCurrentData.value = null
  } else {
    newsRead({ dataId: item.dataId })
    detailCurrentData.value = item
    if (!item.isRead) {
      emit('read', item.topicIds)
    }

    item.isRead = true
  }
}

/**
 * @description: 全部新闻标记为已读
 * @return {*}
 */
async function handleReadAll() {
  if (props.activeGroupInfo?.unReadCount === 0) {
    message.info('暂无未读消息')
    return false
  }
  try {
    dataListLoading.value = true
    const { message: msg } = await newsReadAllTopicNews({ topicId: activityGroupId.value })
    await refresh()
    message.success(msg)
    // 读取全部后，刷新列表
    emit('readAll')
    dataListLoading.value = false
  } catch (error) {
    dataListLoading.value = false
    console.error(error)
  }
}

/**
 * @description: 刷新列表方法
 * @return {*}
 */
async function refreshPageData() {
  detailCurrentData.value = null
  loading.value = true
  await refresh()
  loading.value = false
  handleNewsCardClick(intelligenceList.value[0])
}

/**
 * @description: 替换特殊空格为&nbsp，用于解决特殊空格在HTML中的显示问题
 * @param {*} text
 * @return {*}
 */
function clearHtmlTag(html: string): string {
  if (!html) {
    return ''
  }
  // 移除 <script> 标签及其内容
  html = html.replace(/<script.*?>.*?<\/script>/gs, '')
  // 移除 <style> 标签及其内容
  html = html.replace(/<style.*?>.*?<\/style>/gs, '')
  html = html.replace(/<!--.*?-->/gs, '')
  // 移除所有 HTML 标签
  html = html.replace(/<[^>]+>/g, '')
  // 替换多个连续的空格或制表符为一个空格
  html = html.replace(/[\t ]+/g, ' ')
  // 替换多个连续的换行符为一个换行符
  html = html.replace(/ \n+/g, '\n')
  html = html.replace(/[\r\n]+/g, '\n')
  // 移除开头的换行符
  html = html.replace(/^\n/, '')
  return html
}

const markdown = MarkdownIt({
  breaks: true,
  html: true,
  linkify: true
})

const contentText = (text: string) => {
  const replaceText = text.replace(/\*\*/g, '\u200B**\u200B') // 添加零宽空格 https://juejin.cn/post/7064565848421171213
  return markdown.render(replaceText)
}

// 如果专题id变动，刷新列表
watch(
  () => props.activityGroupId,
  () => {
    handleFilterReset()
    formDataReset()
    Promise.all([getLabelMap(), refreshPageData()])
  }
)

onMounted(() => {
  refreshPageData()
})
</script>

<style lang="less" scoped>
@keyframes check {
  from,
  to {
    margin-right: 0;
    -webkit-mask: linear-gradient(to right, #fff calc(100% - 30px), transparent);
  }
}
@keyframes appear {
  from,
  to {
    counter-increment: num 0;
  }
}
.ellipsisTag {
  width: 100%;
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.tipsPoint {
  position: relative;
  &::after {
    content: '';
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: red;
    left: 5px;
    top: 48px;
  }
}
</style>
