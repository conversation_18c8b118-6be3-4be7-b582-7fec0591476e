<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-02-27 15:13:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-05-22 16:51:45
 * @FilePath: /global-intelligence-web/src/views/intelligence/index.vue
 * @Description: 
-->
<template>
  <div class="flex h100vh bg-#fff">
    <div class="min-w-260px max-w-260px h100% border-c-#050505-6 border-r-1px border-solid">
      <GroupList
        ref="groupListRef"
        v-model:activityGroupId="activityGroupId"
        v-model:activeGroupInfo="activeGroupInfo"
      />
    </div>

    <div class="min-w-360px max-w-360px h100% border-c-#050505-6 border-r-1px border-solid">
      <IntelligenceList
        :activityGroupId="activityGroupId"
        :activeGroupInfo="activeGroupInfo"
        v-model:detailCurrentData="detailCurrentData"
        v-model:loading="loading"
        @read="groupListRef?.singleNewsReading"
        @readAll="groupListRef?.getData()"
      />
    </div>

    <div ref="detailContainerRef" class="flex-1 overflow-auto">
      <template v-if="loading === false">
        <template v-if="detailCurrentData !== null">
          <IntelligenceDetail :intelligenceInfo="detailCurrentData" :key="detailCurrentData.dataId" />
        </template>
        <template v-else>
          <div class="pt56px h100% flex-center-center">
            <a-empty :image="Empty.PRESENTED_IMAGE_SIMPLE"></a-empty>
          </div>
        </template>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, useTemplateRef, watch } from 'vue'
import type { newsPageResType } from '~/types/api/news/page'
import IntelligenceList from './components/intelligenceList.vue'
import IntelligenceDetail from './components/intelligenceDetail.vue'
import GroupList from './components/groupList.vue'
import { Empty } from 'ant-design-vue'
import type { NewsTopicSubscribeTopicResType } from '~/types/api/newsTopic/subscribe'

defineOptions({ name: 'intelligence-index' })

const activityGroupId = ref<string>('全部')
const activeGroupInfo = ref<NewsTopicSubscribeTopicResType | undefined>()
const loading = ref(false)
const detailCurrentData = ref<newsPageResType | null>(null)

const groupListRef = useTemplateRef('groupListRef')
const detailContainerRef = useTemplateRef('detailContainerRef')

// 监听详情数据变化，滚动到顶部
watch(detailCurrentData, () => detailContainerRef.value?.scrollTo({ top: 0 }))
</script>

<style scoped></style>
