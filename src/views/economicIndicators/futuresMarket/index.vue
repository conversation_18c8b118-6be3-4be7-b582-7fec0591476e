<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-04-27 10:38:37
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-05-22 18:13:22
 * @FilePath: /global-intelligence-web/src/views/economicIndicators/futuresMarket/index.vue
 * @Description: 商品价格
-->
<template>
  <div class="futuresMarket">
    <div class="titleBox p-16px flex-between-center">
      <p class="fw-550 color-#000-88 fs-20">商品价格</p>
    </div>

    <div class="px-16px">
      <!-- 过滤表单 -->
      <a-form layout="inline" class="mb-16px">
        <a-form-item>
          <a-input v-model:value.trim="formData.name" placeholder="名称或代码" allow-clear @pressEnter="handleSearch" />
        </a-form-item>
        <a-form-item>
          <a-select
            v-model:value="formData.exchange"
            placeholder="交易所"
            style="width: 200px"
            allow-clear
            show-search
            :options="exchangeOptions"
            :filter-option="(input: string, option: any) => option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0"
          >
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-select
            v-model:value="formData.goods"
            placeholder="商品类型"
            style="width: 200px"
            allow-clear
            show-search
            :options="goodsOptions"
            :filter-option="(input: string, option: any) => option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0"
          >
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button @click="handleReset">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :loading="loading"
        :data-source="dataList"
        :pagination="pageParams"
        size="small"
        :scroll="{ x: 2300, y: tableHeight }"
      >
        <template #bodyCell="{ column, text }">
          <template v-if="column.dataIndex === 'turnover'"> {{ formatNumber(text) }}</template>
          <template v-if="column.dataIndex === 'transactionVolume'">{{ formatNumber(text) }} </template>
          <template v-if="column.dataIndex === 'riseFallAmount'">
            <!-- 正数红色 负数绿色 -->
            <span :class="Number(text) > 0 ? 'color-#f5222d' : Number(text) < 0 ? 'color-#52c41a' : ''">
              {{ Number(text) > 0 ? '+' : '' }}{{ text }}
            </span>
          </template>
          <template v-if="column.dataIndex === 'riseFallRate'">
            <span
              :class="[
                Number(text.replace('%', '')) > 0
                  ? 'color-#f5222d'
                  : Number(text.replace('%', '')) < 0
                  ? 'color-#52c41a'
                  : '',
                'flex-center-center pr-16px'
              ]"
            >
              <iconfontIcon v-if="Number(text.replace('%', '')) > 0" icon="icon-caret-up-small"></iconfontIcon>
              <iconfontIcon v-else-if="Number(text.replace('%', '')) < 0" icon="icon-caret-down-small"></iconfontIcon>
              {{ text !== '-' ? `${text.replace('%', '')}%` : `${text}` }}
            </span>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import useListLoading from '@/hooks/useListLoading'
import type { futuresMarketPageReqType } from '~/types/api/data/futuresMarketPage'
import { dataFuturesMarketPage } from '@/api/api'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { toNumber } from 'lodash-es'
import { useWindowSize } from '@vueuse/core'
import useTableHeight from '@/hooks/useTableHeight'

// 表单数据
const formData = ref<futuresMarketPageReqType>({
  name: '',
  exchange: undefined,
  goods: undefined
})

// 交易所选项
const exchangeOptions = [
  { label: '上期所', value: '上期所' },
  { label: '上期能源', value: '上期能源' },
  { label: '大商所', value: '大商所' },
  { label: '郑商所', value: '郑商所' },
  { label: '广期所', value: '广期所' },
  { label: '芝加哥CBOT', value: '芝加哥CBOT' },
  { label: '纽约NYMEX', value: '纽约NYMEX' },
  { label: '纽约COMEX', value: '纽约COMEX' },
  { label: '纽约NYBOT', value: '纽约NYBOT' },
  { label: '伦敦LME', value: '伦敦LME' },
  { label: '洲际ICE', value: '洲际ICE' },
  { label: '新加坡SGX', value: '新加坡SGX' },
  { label: '马来西亚BMD', value: '马来西亚BMD' }
]

// 商品选项
const goodsOptions = [
  { label: '液化石油气', value: '液化石油气' },
  { label: 'PVC', value: 'PVC' },
  { label: '苯乙烯', value: '苯乙烯' },
  { label: '淀粉', value: '淀粉' },
  { label: '大豆', value: '大豆' },
  { label: '豆粕', value: '豆粕' },
  { label: '豆油', value: '豆油' },
  { label: '粳米', value: '粳米' },
  { label: '鸡蛋', value: '鸡蛋' },
  { label: '胶合板', value: '胶合板' },
  { label: '焦煤', value: '焦煤' },
  { label: '焦炭', value: '焦炭' },
  { label: '聚丙烯', value: '聚丙烯' },
  { label: '生猪', value: '生猪' },
  { label: '塑料', value: '塑料' },
  { label: '铁矿石', value: '铁矿石' },
  { label: '纤维板', value: '纤维板' },
  { label: '乙二醇', value: '乙二醇' },
  { label: '玉米', value: '玉米' },
  { label: '原木', value: '原木' },
  { label: '棕榈油', value: '棕榈油' },
  { label: '多晶硅', value: '多晶硅' },
  { label: '工业硅', value: '工业硅' },
  { label: '碳酸锂', value: '碳酸锂' },
  { label: '铝', value: '铝' },
  { label: '铜', value: '铜' },
  { label: '铅', value: '铅' },
  { label: '镍', value: '镍' },
  { label: '锡', value: '锡' },
  { label: '锌', value: '锌' },
  { label: '黄金', value: '黄金' },
  { label: '氢氧化锂', value: '氢氧化锂' },
  { label: '白银', value: '白银' },
  { label: '棉花', value: '棉花' },
  { label: '糖', value: '糖' },
  { label: '原油', value: '原油' },
  { label: '燃油', value: '燃油' },
  { label: '天然气', value: '天然气' },
  { label: '钯金', value: '钯金' },
  { label: '铂金', value: '铂金' },
  { label: '汽油', value: '汽油' },
  { label: '橡胶', value: '橡胶' },
  { label: '乙醇', value: '乙醇' },
  { label: '小麦', value: '小麦' },
  { label: '燕麦', value: '燕麦' },
  { label: '稻谷', value: '稻谷' },
  { label: '柴油', value: '柴油' },
  { label: '国际铜', value: '国际铜' },
  { label: '不锈钢', value: '不锈钢' },
  { label: '丁二烯胶', value: '丁二烯胶' },
  { label: '沥青', value: '沥青' },
  { label: '螺纹钢', value: '螺纹钢' },
  { label: '热卷', value: '热卷' },
  { label: '天然橡胶', value: '天然橡胶' },
  { label: '线材', value: '线材' },
  { label: '氧化铝', value: '氧化铝' },
  { label: '纸浆', value: '纸浆' },
  { label: 'PTA', value: 'PTA' },
  { label: '白糖', value: '白糖' },
  { label: '玻璃', value: '玻璃' },
  { label: '菜粕', value: '菜粕' },
  { label: '菜油', value: '菜油' },
  { label: '纯碱', value: '纯碱' },
  { label: '短纤', value: '短纤' },
  { label: '动力煤', value: '动力煤' },
  { label: '对二甲苯', value: '对二甲苯' },
  { label: '硅铁', value: '硅铁' },
  { label: '红枣', value: '红枣' },
  { label: '花生', value: '花生' },
  { label: '甲醇', value: '甲醇' },
  { label: '锰硅', value: '锰硅' },
  { label: '棉纱', value: '棉纱' },
  { label: '尿素', value: '尿素' },
  { label: '苹果', value: '苹果' },
  { label: '瓶级聚酯切片', value: '瓶级聚酯切片' },
  { label: '普麦', value: '普麦' },
  { label: '强麦', value: '强麦' },
  { label: '烧碱', value: '烧碱' },
  { label: '油菜籽', value: '油菜籽' }
]

// 表格列配置
const columns = [
  { title: '名称', dataIndex: 'name', align: 'center', fixed: 'left' },
  { title: '代码', dataIndex: 'code', align: 'center', fixed: 'left' ,width:80},
  { title: '交易所', dataIndex: 'exchange', align: 'center' ,width:140},
  { title: '商品类型', dataIndex: 'itemType', align: 'center', width: 140 },
  { title: '报价单位', dataIndex: 'unit', align: 'center', width: 100 },
  { title: '最新价', dataIndex: 'lastPrice', align: 'center', width: 140 },
  { title: '涨跌额', dataIndex: 'riseFallAmount', align: 'center', width: 120 },
  { title: '涨跌幅', dataIndex: 'riseFallRate', align: 'center', width: 120 },
  { title: '今开', dataIndex: 'beginPrice', align: 'center', width: 140 },
  { title: '最高', dataIndex: 'highestPrice', align: 'center', width: 140 },
  { title: '最低', dataIndex: 'lowestPrice', align: 'center', width: 140 },
  { title: '昨结', dataIndex: 'yesterdayEndPrice', align: 'center', width: 140 },
  { title: '成交量', dataIndex: 'turnover', align: 'center', width: 140 },
  { title: '成交额', dataIndex: 'transactionVolume', align: 'center', width: 140 },
  { title: '买盘(外盘)', dataIndex: 'outerDisc', align: 'center', width: 140 },
  { title: '卖盘(内盘)', dataIndex: 'insideDisc', align: 'center', width: 140 },
  { title: '持仓量', dataIndex: 'openInterest', align: 'center', width: 140 }
]

const tableHeight = useTableHeight(800, ['.futuresMarket .titleBox', '.futuresMarket .ant-form'])

// 使用列表加载hook
const { dataList, refresh, pageParams, loading } = useListLoading(dataFuturesMarketPage, formData, {
  pageParams: { pageSize: 50 }
})

// 查询
function handleSearch() {
  pageParams.value.current = 1
  refresh()
}

// 转换数字方法，添加逗号方便阅读，超过1亿用亿作为单位，超过1万用万作为单位
function formatNumber(num: number): string {
  const _num = toNumber(num)
  if (_num >= 100000000) {
    return (_num / 100000000).toFixed(2) + '亿'
  } else if (_num >= 10000) {
    return (_num / 10000).toFixed(2) + '万'
  } else {
    return _num.toString()
  }
}

// 重置
function handleReset() {
  formData.value = {
    name: '',
    exchange: undefined,
    goods: undefined
  }
  refresh()
}
</script>

<style lang="less" scoped></style>
