<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-04-27 10:38:37
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-05-09 13:18:07
 * @FilePath: /global-intelligence-web/src/views/economicIndicators/economicIndicators/index.vue
 * @Description: 经济指标
-->
<template>
  <div class="economicIndicators">
    <div class="titleBox p-16px flex-between-center">
      <p class="fw-550 color-#000-88 fs-20">经济指数</p>
    </div>

    <div class="px-16px">
      <a-segmented v-model:value="activeKey" :options="['经济指标', '指标更新']" />
      <keep-alive>
        <component :is="activeKey === '经济指标' ? 'EconomicIndicatorsTable' : 'IndicatorUpdateTable'" />
      </keep-alive>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import EconomicIndicatorsTable from './components/economicIndicatorsTable.vue'
import IndicatorUpdateTable from './components/indicatorUpdateTable.vue'

defineOptions({ components: { EconomicIndicatorsTable, IndicatorUpdateTable } })

const activeKey = ref<'经济指标' | '指标更新'>('经济指标') // 当前选中的分组类型
</script>

<style scoped></style>
