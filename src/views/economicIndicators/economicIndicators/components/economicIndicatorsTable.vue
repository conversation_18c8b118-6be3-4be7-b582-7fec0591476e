<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-04-28 12:13:52
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-05-09 14:05:40
 * @FilePath: /global-intelligence-web/src/views/economicIndicators/economicIndicators/components/economicIndicatorsTable.vue
 * @Description: 
-->
<template>
  <div class="w100% economicIndicatorsTable">
    <a-tabs v-model:activeKey="formData.continent" @change="refresh">
      <a-tab-pane v-for="item in tabList" :key="item.value" :tab="item.label"></a-tab-pane>
      <template #rightExtra></template>
    </a-tabs>

    <a-table
      :columns="columns"
      :data-source="dataList"
      :pagination="pageParams"
      :loading="loading"
      size="small"
      :scroll="{ x: 1400, y: tableHeight }"
    >
      <template #headerCell="{ title, column }">
        <template v-if="column.dataIndex === 'gdp'">
          <div>
            <p>{{ title }}</p>
            <span class="text-gray fs-12px"> 万亿美元</span>
          </div>
        </template>

        <template
          v-if="
            ['gdpUpRate', 'interestRate', 'inflationRate', 'joblessRate', 'governmentBudgeRate', 'debtRate'].includes(
              column.dataIndex
            )
          "
        >
          <div>
            <p>{{ title }}</p>
            <span class="text-gray fs-12px"> %</span>
          </div>
        </template>

        <template v-if="column.dataIndex === 'population'">
          <div>
            <p>{{ title }}</p>
            <span class="text-gray fs-12px"> 万</span>
          </div>
        </template>
      </template>
      <template #bodyCell="{ text, column }">
        <template v-if="column.dataIndex === 'gdp'">
          {{ text.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') }}
        </template>
        <template v-if="column.dataIndex === 'population'">
          {{ text.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') }}
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import useListLoading from '@/hooks/useListLoading'
import { dataEconomicIndicatorPage } from '@/api/api'
import type { economicIndicatorPageReqType } from '~/types/api/data/economicIndicatorPage.d'
import useTableHeight from '@/hooks/useTableHeight'

const tabList = [
  { label: '世界', value: '' },
  { label: '亚洲', value: '亚洲' },
  { label: '北美洲', value: '北美洲' },
  { label: '南美洲', value: '南美洲' },
  { label: '欧洲', value: '欧洲' },
  { label: '非洲', value: '非洲' },
  { label: '大洋洲', value: '大洋洲' }
]

const columns = [
  { title: '国家', dataIndex: 'cleanCountry', align: 'center' },
  { title: 'GDP', dataIndex: 'gdp', align: 'center' },
  { title: 'GDP增长率', dataIndex: 'gdpUpRate', align: 'center' },
  { title: '利率', dataIndex: 'interestRate', align: 'center' },
  { title: '通货膨胀率', dataIndex: 'inflationRate', align: 'center' },
  { title: '失业率', dataIndex: 'joblessRate', align: 'center' },
  { title: '政府预算余额/GDP', dataIndex: 'governmentBudgeRate', align: 'center' },
  { title: '债务/GDP', dataIndex: 'debtRate', align: 'center' },
  { title: '人口', dataIndex: 'population', align: 'center' }
]

// 查询参数
const formData = ref<economicIndicatorPageReqType>({ continent: tabList[0].value })

// 使用列表加载hook
const { dataList, pageParams, loading, refresh } = useListLoading(dataEconomicIndicatorPage, formData, {
  pageParams: { pageSize: 50 }
})

const tableHeight = useTableHeight(
  800,
  [
    '.economicIndicators .titleBox',
    '.economicIndicators .ant-segmented',
    '.economicIndicators .economicIndicatorsTable .ant-tabs'
  ],
  { tableHeight: 61 }
)
</script>

<style scoped></style>
