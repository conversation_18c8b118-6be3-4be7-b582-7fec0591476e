<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-04-27 10:38:37
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-05-07 20:07:04
 * @FilePath: /global-intelligence-web/src/views/economicIndicators/foreignExchange/index.vue
 * @Description: 外汇
-->
<template>
  <div class="pb-4px">
    <div class="titleBox p-16px flex-between-center">
      <p class="fw-550 color-#000-88 fs-20">外汇</p>
    </div>

    <a-spin :spinning="loading">
      <div class="pl-16px pr-32px flex">
        <div ref="leftTableRef" class="w-50%">
          <a-table
            :columns="foreignExchangeColumns"
            :data-source="foreignExchangeList"
            :pagination="false"
            size="small"
            :customRow="
              (record:foreignExchangeLastResType) => {
                return {
                  class: ['cursor-pointer', currentForeignExchange?.currencyPairCode === record.currencyPairCode?'bg-#f9f8fe':''],
                  onclick: () => {
                    handleRowClick(record)
                  }
                }
              }
            "
          >
            <template #bodyCell="{ column, text }">
              <template v-if="column.dataIndex === 'currencyPair'">
                <a-space>
                  <img class="h-12px" :src="foreignExchangeIconMap[text]" alt="" />
                  <span>{{ text }}</span>
                </a-space>
              </template>
              <template v-if="column.dataIndex === 'upDown'">
                <span
                  :class="[
                    Number(text) > 0 ? 'color-#f5222d' : Number(text) < 0 ? 'color-#52c41a' : '',
                    'flex-center-center pr-16px'
                  ]"
                >
                  <iconfontIcon v-if="text > 0" icon="icon-caret-up-small"></iconfontIcon>
                  <iconfontIcon v-else-if="text < 0" icon="icon-caret-down-small"></iconfontIcon>
                  {{ text }}
                </span>
              </template>
            </template>
          </a-table>
        </div>

        <div class="px-16px">
          <div class="h-full w-1px bg-gray-100"></div>
        </div>

        <div class="w-50%">
          <template v-if="historyData">
            <div ref="currentForeignExchangeRef" class="flex-center-center flex-direction-column bg-f0eefd py-16px">
              <a-space class="mb-8px">
                <img :src="currentForeignExchangeIcon" class="h16px" />
                <span>{{ currentForeignExchange?.currencyPair }}</span>
              </a-space>

              <p class="mb-8px fs-32px fw-600">{{ currentForeignExchange?.medianPrice || '' }}</p>

              <span
                :class="[
                  Number(currentForeignExchange?.upDown) > 0
                    ? 'color-#f5222d'
                    : Number(currentForeignExchange?.upDown) < 0
                    ? 'color-#52c41a'
                    : '',
                  'mb-8px flex items-center pr-12px fs-18'
                ]"
              >
                <iconfontIcon
                  v-if="Number(currentForeignExchange?.upDown) > 0"
                  icon="icon-caret-up-small"
                ></iconfontIcon>
                <iconfontIcon
                  v-if="Number(currentForeignExchange?.upDown) < 0"
                  icon="icon-caret-down-small"
                ></iconfontIcon>
                {{ currentForeignExchange?.upDown }}
              </span>

              <span class="text-gray">
                {{
                  currentForeignExchange?.publishTime
                    ? dayjs(currentForeignExchange?.publishTime).format('YYYY-MM-DD')
                    : ''
                }}
              </span>
            </div>

            <div>
              <vChart
                ref="vChartRef"
                class="h-320px"
                :option="chartOption"
                :init-options="{ locale: 'ZH' }"
                :autoresize="false"
              />
            </div>

            <div class="w100% overflow-hidden rightTable">
              <a-table
                :columns="historyColumns"
                :data-source="historyData"
                :pagination="false"
                size="small"
                :scroll="{ y: rightTableHeight, x: false }"
              >
                <template #bodyCell="{ text, column }">
                  <template v-if="column.dataIndex === 'publishTime'">
                    {{ dayjs(text).format('YYYY-MM-DD') }}
                  </template>

                  <template v-if="column.dataIndex === 'upDown'">
                    <span
                      :class="[
                        Number(text) > 0 ? 'color-#f5222d' : Number(text) < 0 ? 'color-#52c41a' : '',
                        'flex-center-center pr-16px'
                      ]"
                    >
                      <iconfontIcon v-if="text > 0" icon="icon-caret-up-small"></iconfontIcon>
                      <iconfontIcon v-else-if="text < 0" icon="icon-caret-down-small"></iconfontIcon>
                      {{ text }}
                    </span>
                  </template>
                </template>
              </a-table>
            </div>
          </template>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { dataForeignExchangeHistory, dataForeignExchangeLast } from '@/api/api'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { computed, nextTick, onMounted, ref, useTemplateRef, watch } from 'vue'
import type { foreignExchangeLastResType } from '~/types/api/data/foreignExchangeLast'
import useRequest from '@/hooks/useRequest'
import dayjs from 'dayjs'
import { use, type ComposeOption } from 'echarts/core'
import { LineChart, type LineSeriesOption } from 'echarts/charts'
import {
  TooltipComponent,
  GridComponent,
  type GridComponentOption,
  type TooltipComponentOption
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import VChart from 'vue-echarts'
import { add, ceil, floor, maxBy, minBy, subtract } from 'lodash-es'
import { useWindowSize } from '@vueuse/core'
type EChartsOption = ComposeOption<TooltipComponentOption | GridComponentOption | LineSeriesOption>
use([TooltipComponent, GridComponent, LineChart, CanvasRenderer])

// 表格列配置
const foreignExchangeColumns = [
  { title: '货币对', dataIndex: 'currencyPair' },
  { title: '货币对代码', dataIndex: 'currencyPairCode', align: 'center', width: '18%' },
  { title: '中间价', dataIndex: 'medianPrice', align: 'center', width: '18%' },
  { title: '涨跌', dataIndex: 'upDown', align: 'center', width: '18%' }
]

// 当前选中的外汇
const currentForeignExchange = ref<foreignExchangeLastResType>()
async function handleRowClick(record: foreignExchangeLastResType) {
  await getHistoryData()
  currentForeignExchange.value = record
}
const foreignExchangeIconMap: Record<string, string> = {
  '美元/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/USD.jpg', // 'USD/CNY'
  '欧元/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/EUR.jpg', // 'EUR/CNY'
  '100日元/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/JPY.jpg', // '100JPY/CNY'
  '港元/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/HKD.jpg', // 'HKD/CNY'
  '英镑/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/GBP.jpg', // 'GBP/CNY'
  '澳元/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/AUD.jpg', // 'AUD/CNY'
  '新西兰元/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/NZD.jpg', // 'NZD/CNY'
  '新加坡元/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/SGD.jpg', // 'SGD/CNY'
  '瑞士法郎/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/CHF.jpg', // 'CHF/CNY'
  '加元/人民币': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/CAD.jpg', // 'CAD/CNY'
  '人民币/澳门元': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/MOP.jpg', // 'CNY/MOP'
  '人民币/马来西亚林吉特': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/MYR.jpg', // 'CNY/MYR'
  '人民币/俄罗斯卢布': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/RUB.jpg', // 'CNY/RUB'
  '人民币/南非兰特': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/ZAR.jpg', // 'CNY/ZAR'
  '人民币/韩元': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/KRW.jpg', // 'CNY/KRW'
  '人民币/阿联酋迪拉姆': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/AED.jpg', // 'CNY/AED'
  '人民币/沙特里亚尔': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/SAR.jpg', // 'CNY/SAR'
  '人民币/匈牙利福林': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/HUF.jpg', // 'CNY/HUF'
  '人民币/波兰兹罗提': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/PLN.jpg', // 'CNY/PLN'
  '人民币/丹麦克朗': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/DKK.jpg', // 'CNY/DKK'
  '人民币/瑞典克朗': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/SEK.jpg', // 'CNY/SEK'
  '人民币/挪威克朗': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/NOK.jpg', // 'CNY/NOK'
  '人民币/土耳其里拉': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/TRY.jpg', // 'CNY/TRY'
  '人民币/墨西哥比索': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/MXN.jpg', // 'CNY/MXN'
  '人民币/泰铢': 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/assets/images/flag/THB.jpg' // 'CNY/THB'
}
const currentForeignExchangeIcon = computed(() => {
  if (!currentForeignExchange.value) return ''
  return foreignExchangeIconMap[currentForeignExchange.value.currencyPair as string]
})

const {
  dataList: foreignExchangeList,
  loading: foreignExchangeLoading,
  getData: getForeignExchangeList
} = useRequest(dataForeignExchangeLast, {}, { immediateReqData: false })

const historyColumns = [
  { title: '日期', dataIndex: 'publishTime', align: 'center', width: '33%' },
  { title: '中间价', dataIndex: 'medianPrice', align: 'center', width: '33%' },
  { title: '涨跌', dataIndex: 'upDown', align: 'center', width: '33%' }
]
const getHistoryParams = computed(() => ({
  currencyPairCode: currentForeignExchange.value?.currencyPairCode || '',
  beginTime: dayjs().subtract(1, 'M').format('YYYY-MM-DD'),
  endTime: dayjs().format('YYYY-MM-DD')
}))
const {
  dataList: historyData,
  getData: getHistoryData,
  loading: historyLoading
} = useRequest(dataForeignExchangeHistory, getHistoryParams, { immediateReqData: false })

// 图表部分
const vChartRef = useTemplateRef<InstanceType<typeof VChart>>('vChartRef')
const chartOption = computed(() => {
  const data = historyData.value?.map(item => [item.publishTime, item.medianPrice]).reverse() || []
  const dataMax = ceil(add(maxBy(historyData.value, 'medianPrice')?.medianPrice || 0, 0.03), 2)
  const dataMin = floor(subtract(minBy(historyData.value, 'medianPrice')?.medianPrice || 0, 0.03), 2)

  const options: EChartsOption = {
    tooltip: {
      trigger: 'axis',
      formatter: params => {
        const { value, seriesName } = params[0]
        return dayjs(value[0]).format('YYYY-MM-DD') + '</br>' + `${seriesName}：${value[1]}`
      }
    },
    xAxis: {
      type: 'category',
      axisLabel: {
        formatter: value => dayjs(value).format('M月D日'),
        interval: 5,
        alignMaxLabel: 'right',
        showMaxLabel: true
      },
      boundaryGap: false
    },
    yAxis: {
      type: 'value',
      min: dataMin,
      max: dataMax,
      scale: true
    },
    grid: {
      containLabel: true,
      left: 'left',
      right: 0,
      top: 40,
      bottom: 40
    },
    series: [
      {
        name: '中间价',
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: { color: '#6553ee' },
        areaStyle: { opacity: 0.2, color: '#6553ee' },
        emphasis: { areaStyle: { opacity: 0.2, color: '#6553ee' } },
        data
      }
    ]
  }

  return options
})

const { width, height } = useWindowSize()
watch([width, height], () => {
  vChartRef.value?.resize()
})

const loading = computed(() => foreignExchangeLoading.value || historyLoading.value)

// 计算表格高度，保证左右高度一致
const leftTableRef = useTemplateRef<HTMLElement>('leftTableRef')
const currentForeignExchangeRef = useTemplateRef<HTMLElement>('currentForeignExchangeRef')
const rightTableHeight = ref(400)
async function setRightTableHeight() {
  await nextTick()
  rightTableHeight.value =
    (leftTableRef.value?.clientHeight || 0) - (currentForeignExchangeRef.value?.clientHeight || 0) - 320 - 40 // 400是图表高度，40是表头高度
}

onMounted(async () => {
  await getForeignExchangeList()
  currentForeignExchange.value = foreignExchangeList.value?.[0]
  await getHistoryData()
  setRightTableHeight()
})
</script>

<style lang="less" scoped>
.rightTable {
  border-bottom: 1px solid #f0f0f0;
}
</style>
