<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-18 17:35:47
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-21 17:55:45
 * @FilePath: /global-intelligence-web/src/views/system/user/resetPasswordModal.vue
 * @Description: 
-->
<template>
  <a-modal v-model:open="visible" title="重置密码" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="close">
    <a-spin :spinning="confirmLoading">
      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :labelCol="{ span: 4 }"
        :wrapperCol="{ span: 20 }"
        class="userModal"
      >
        <a-form-item label="登录密码" name="password">
          <!-- <a-input-password autoComplete="new-password" v-model:value="form.password" placeholder="登录密码" /> -->
          <a-input-group class="randomPassword">
            <a-input-password
              autoComplete="new-password"
              v-model:value="form.password"
              placeholder="登录密码"
              style="width: calc(100% - 88px)"
            />
            <a-button type="primary" @click="randomPassword(8)">随机密码</a-button>
          </a-input-group>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts" name="userModal">
import { ref } from 'vue'
import { userResetPassword } from '@/api/api'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'

const emit = defineEmits(['ok', 'close'])
const formRef = ref()
const confirmLoading = ref(false)
const visible = ref(false)
const form = ref({
  id: '',
  password: ''
})
const rules: Record<string, Rule[]> = {
  password: [
    { required: true, message: '请输入登录密码!' },
    {
      pattern: /^\S*(?=\S{8,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*?])\S*$/,
      message: '密码必须包含大写字母、小写字母、数字和特殊字符，且长度不小于8位'
    }
  ]
}

function onOpen(id: string) {
  form.value.id = id
  visible.value = true
}

async function handleOk() {
  // 触发表单验证
  try {
    await formRef.value.validateFields()
    console.log('form.value: ', form.value)
    confirmLoading.value = true
    const { message: msg } = await userResetPassword(form.value)
    message.success(msg)
    emit('ok')
    confirmLoading.value = false
    close()
  } catch (error) {
    console.error(error)
    confirmLoading.value = false
  }
}

function close() {
  emit('close')
  formRef.value.resetFields()
  visible.value = false
  form.value = {
    id: '',
    password: ''
  }
}

function randomPassword(length: number) {
  // 定义字符集
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const lowercase = uppercase.toLowerCase()
  const numbers = '0123456789'
  const symbols = '!@#$%^&*?'

  // 确保每个字符类别至少包含一个
  const allChars = [
    uppercase[Math.floor(Math.random() * uppercase.length)],
    lowercase[Math.floor(Math.random() * lowercase.length)],
    numbers[Math.floor(Math.random() * numbers.length)],
    symbols[Math.floor(Math.random() * symbols.length)]
  ]

  // 合并所有可用字符
  const charPool = uppercase + lowercase + numbers + symbols

  // 填充剩余长度
  while (allChars.length < length) {
    const randomChar = charPool[Math.floor(Math.random() * charPool.length)]
    allChars.push(randomChar)
  }

  // 打乱顺序（Fisher-Yates 洗牌算法）
  for (let i = allChars.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[allChars[i], allChars[j]] = [allChars[j], allChars[i]]
  }

  // return allChars.join('')
  form.value.password = allChars.join('')
}

defineExpose({
  onOpen
})
</script>

<style lang="less" scoped>
.userModal {
  .randomPassword {
    .ant-btn {
      border-radius: 0 6px 6px 0;
    }
  }
}
</style>
