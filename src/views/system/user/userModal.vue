<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-18 17:35:47
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-04-28 18:31:25
 * @FilePath: /global-intelligence-web/src/views/system/user/userModal.vue
 * @Description: 
-->
<template>
  <a-modal
    v-model:open="visible"
    :title="`${modalTitle}用户`"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="close"
  >
    <a-spin :spinning="confirmLoading">
      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :labelCol="{ span: 5 }"
        :wrapperCol="{ span: 19 }"
        class="userModal"
      >
        <a-form-item label="用户账号" name="email">
          <a-input
            v-model:value="form.email"
            autoComplete="new-email"
            placeholder="邮箱"
            :disabled="modalTitle === '编辑'"
          ></a-input>
        </a-form-item>
        <template v-if="modalTitle === '新增'">
          <!-- <a-form-item label="用户账号" name="phone">
            <a-input
              v-model:value="form.phone"
              autoComplete="new-username"
              placeholder="手机号"
            ></a-input>
          </a-form-item> -->

          <a-form-item label="登录密码" name="password">
            <!-- <a-input-password autoComplete="new-password" v-model:value="form.password" placeholder="登录密码" /> -->
            <a-input-group class="randomPassword">
              <a-input-password
                autoComplete="new-password"
                v-model:value="form.password"
                placeholder="登录密码"
                style="width: calc(100% - 88px)"
              />
              <a-button type="primary" @click="randomPassword(8)">随机密码</a-button>
            </a-input-group>
          </a-form-item>

          <a-form-item label="确认密码" name="checkPassword">
            <a-input-password v-model:value="form.checkPassword" placeholder="再次确认密码" />
          </a-form-item>
        </template>

        <a-form-item label="用户姓名" name="userName">
          <a-input v-model:value="form.userName" placeholder="真实姓名"></a-input>
        </a-form-item>

        <a-form-item label="角色分配" name="roleIds">
          <a-select
            v-model:value="form.roleIds"
            placeholder="角色类型"
            mode="multiple"
            show-search
            :filter-option="(input: string, option: any) => option.roleName.toLowerCase().indexOf(input.toLowerCase()) >= 0"
            :options="roleList"
            :fieldNames="{ label: 'roleName', value: 'id' }"
          >
          </a-select>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts" name="userModal">
import { ref } from 'vue'
import { userCreate, userUpdate, rolePage } from '@/api/api'
import { message } from 'ant-design-vue'
import { isUndefined } from 'lodash-es'
import type { CreatedUserType, UserType, UpdateUserType } from '~/types/api/user/common'
import type { Rule } from 'ant-design-vue/es/form'
import useListLoading from '@/hooks/useListLoading'

interface FormType extends CreatedUserType, UpdateUserType {
  checkPassword?: string
}

const emit = defineEmits(['ok', 'close'])
const formRef = ref()
const modalTitle = ref('')
const confirmLoading = ref(false)
const visible = ref(false)
// const roleList = ref<RoleType[]>()
const { dataList: roleList, getData } = useListLoading(
  rolePage,
  { pageNo: 1, pageSize: 1000 },
  { immediateReqData: false }
)

const form = ref<FormType>({
  id: undefined,
  // phone: '',
  email: '',
  password: '',
  checkPassword: '',
  userName: '',
  roleIds: []
})
const rules: Record<string, Rule[]> = {
  email: [
    { required: true, message: '请输入' },
    {
      pattern:
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      message: '请输入正确邮箱'
    }
  ],
  phone: [
    { required: true, message: '请输入' },
    {
      pattern: /^(?:(?:\+|00)86)?1\d{10}$/,
      message: '请输入正确手机号'
    }
  ],
  password: [
    // { required: true, message: '请输入登录密码!' },
    { required: true, message: '请输入' },
    {
      trigger: 'change',
      pattern: /^\S*(?=\S{8,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*?])\S*$/,
      message: '密码必须包含大写字母、小写字母、数字和特殊字符，且长度不小于8位'
    }
  ],
  checkPassword: [
    // { required: true, message: '请输入密码!' },
    { required: true, message: '请输入' },
    {
      required: true,
      validator: (_rule, value: string) => {
        if (value !== form.value.password) {
          return Promise.reject('两次输入密码不一致!')
        } else {
          return Promise.resolve()
        }
      }
    }
  ],
  userName: [{ required: true, message: '请输入' }],
  roleIds: [{ required: true, message: '请选择' }]
}

function onOpen(openData: { title: '新增' | '编辑'; record?: UserType }) {
  getData()
  modalTitle.value = openData.title
  const record = openData.record

  if (openData.title === '编辑' && !isUndefined(record)) {
    form.value = record
  }

  visible.value = true
}

async function handleOk() {
  // 触发表单验证
  try {
    await formRef.value.validateFields()
    let obj
    if (modalTitle.value === '新增') {
      obj = userCreate
    } else {
      obj = userUpdate
      delete form.value.password
      delete form.value.checkPassword
    }

    console.log('form.value: ', form.value)
    confirmLoading.value = true
    const { message: msg } = await obj(form.value)
    message.success(msg)
    emit('ok')
    confirmLoading.value = false
    close()
  } catch (error) {
    console.error(error)
    confirmLoading.value = false
  }
}

function close() {
  emit('close')
  formRef.value.resetFields()
  visible.value = false
  form.value = {
    id: undefined,
    password: '',
    checkPassword: '',
    phone: '',
    userName: '',
    roleIds: []
  }
}

function randomPassword(length: number) {
  // 定义字符集
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const lowercase = uppercase.toLowerCase()
  const numbers = '0123456789'
  const symbols = '!@#$%^&*?'

  // 确保每个字符类别至少包含一个
  const allChars = [
    uppercase[Math.floor(Math.random() * uppercase.length)],
    lowercase[Math.floor(Math.random() * lowercase.length)],
    numbers[Math.floor(Math.random() * numbers.length)],
    symbols[Math.floor(Math.random() * symbols.length)]
  ]

  // 合并所有可用字符
  const charPool = uppercase + lowercase + numbers + symbols

  // 填充剩余长度
  while (allChars.length < length) {
    const randomChar = charPool[Math.floor(Math.random() * charPool.length)]
    allChars.push(randomChar)
  }

  // 打乱顺序（Fisher-Yates 洗牌算法）
  for (let i = allChars.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[allChars[i], allChars[j]] = [allChars[j], allChars[i]]
  }

  // return allChars.join('')
  form.value.password = allChars.join('')
}

defineExpose({
  onOpen
})
</script>

<style lang="less" scoped>
.userModal {
  .randomPassword {
    .ant-btn {
      border-radius: 0 4px 4px 0;
    }
  }
}
</style>
