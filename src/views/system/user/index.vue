<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-18 16:29:29
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-21 18:42:52
 * @FilePath: /global-intelligence-web/src/views/system/user/index.vue
 * @Description: 
-->
<template>
  <div >
    <div class="titleBox p-16px flex-between-center">
      <p class="fw-550 color-#000-88 fs-20">用户管理</p>
    </div>

    <div class="px-16px">
      <a-form layout="inline" class="mb-16px">
        <a-form-item><a-input v-model:value="formData.keyword" placeholder="关键字"></a-input></a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handlerSizeChange(1, pageParams.pageSize!)">查询</a-button>
            <a-button @click="userModalRef?.onOpen({ title: '新增' })">新增</a-button>
          </a-space>
        </a-form-item>
      </a-form>

      <a-table
        :columns="columns"
        :data-source="dataList"
        :pagination="{
          ...pageParams,
          onChange: handlerSizeChange
        }"
        :loading="loading"
        size="small"
      >
        <template #bodyCell="{ text, column, record }">
          <template v-if="column.dataIndex === 'roleVos'">
            {{ text.map((item: RoleType) => item.roleName).join() }}
          </template>
          <template v-if="column.dataIndex === 'state'">
            {{ text === 'ENABLE' ? '启用' : '禁用' }}
          </template>
          <template v-if="column.dataIndex === 'actions'">
            <MoreIcon :menuList="menuList(record)" @click="menuItem => handlerMoreIconClick(menuItem, record)" />
          </template>
        </template>
      </a-table>
      <ResetPasswordModal ref="resetPasswordModalRef" />
      <UserModal ref="userModalRef" @ok="refresh" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { userDelete, userDisable, userPage } from '@/api/api'
import type { menuItem } from '@/components/tools/moreIcon.vue'
import useListLoading from '@/hooks/useListLoading'
import { message, Modal } from 'ant-design-vue'
import type { ColumnsType } from 'ant-design-vue/es/table'
import type { RolePageReqType } from '~/types/api/role/page'
import UserModal from './userModal.vue'
import { ref } from 'vue'
import type { MenuInfo } from 'ant-design-vue/es/menu/src/interface'
import MoreIcon from '@/components/tools/moreIcon.vue'
import type { UserPageType } from '~/types/api/user/page'
import ResetPasswordModal from './resetPasswordModal.vue'
import type { RoleType } from '~/types/api/role/common'

const formData = ref<RolePageReqType>({
  keyword: ''
})
const userModalRef = ref<InstanceType<typeof UserModal>>()
const resetPasswordModalRef = ref<InstanceType<typeof ResetPasswordModal>>()

const { dataList, refresh, getData, pageParams, loading } = useListLoading(userPage, formData)
const columns: ColumnsType = [
  { title: '用户名称', dataIndex: 'userName' },
  { title: '拥有角色', dataIndex: 'roleVos' },
  { title: '用户状态', dataIndex: 'state' },
  { title: '', dataIndex: 'actions', width: '64px' }
]

function handlerSizeChange(pageNo: number, pageSize: number) {
  pageParams.value.current = pageNo
  pageParams.value.pageSize = pageSize
  getData()
}

// 更多按钮
const menuList = (record: UserPageType): menuItem[] => {
  return [
    { title: '编辑', key: 'edit' },
    { title: record.state === 'ENABLE' ? '禁用' : '启用', key: 'disable' },
    { title: '重置密码', key: 'resetPassword' },
    { title: '删除', key: 'delete' }
  ]
}

// 更多按钮点击事件
function handlerMoreIconClick(clickItem: MenuInfo, record: UserPageType) {
  switch (clickItem.key) {
    case 'edit':
      userModalRef.value?.onOpen({
        title: '编辑',
        record: {
          id: record.id,
          userName: record.userName,
          roleIds: record.roleVos.map(item => item.id),
          email: record.email
        }
      })
      break
    case 'disable':
      Modal.confirm({
        title: '是否禁用该用户？',
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            loading.value = true
            const { message: msg } = await userDisable({ id: record.id, disable: record.state === 'ENABLE' })
            message.success(msg)
            await getData()
            loading.value = false
          } catch (error) {
            console.error(error)
            loading.value = false
          }
        }
      })
      break
    case 'resetPassword':
      resetPasswordModalRef.value?.onOpen(record.id)
      break
    case 'delete':
      Modal.confirm({
        title: '确定删除该专题吗？',
        content: '删除后不可恢复',
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            loading.value = true
            const { message: msg } = await userDelete({ id: record.id! })
            message.success(msg)
            await getData()
            loading.value = false
          } catch (error) {
            console.error(error)
            loading.value = false
          }
        }
      })
      break

    default:
      break
  }
}
</script>

<style scoped></style>
