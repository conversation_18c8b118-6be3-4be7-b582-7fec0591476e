<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-18 16:29:29
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-21 18:42:13
 * @FilePath: /global-intelligence-web/src/views/system/role/index.vue
 * @Description: 
-->
<template>
  <div >
    <div class="titleBox p-16px flex-between-center">
      <p class="fw-550 color-#000-88 fs-20">角色管理</p>
    </div>

    <div class="px-16px">
      <a-form layout="inline" class="mb-16px">
        <a-form-item><a-input v-model:value="formData.keyword" placeholder="关键字"></a-input></a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handlerSizeChange(1, pageParams.pageSize!)">查询</a-button>
            <a-button @click="roleModalRef?.onOpen({ title: '新增' })">新增</a-button>
          </a-space>
        </a-form-item>
      </a-form>

      <a-table
        :columns="columns"
        :data-source="dataList"
        :pagination="{
          ...pageParams,
          onChange: handlerSizeChange
        }"
        :loading="loading"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'actions'">
            <MoreIcon :menuList="menuList" @click="menuItem => handlerMoreIconClick(menuItem, record)" />
          </template>
        </template>
      </a-table>
      <RoleModal ref="roleModalRef" @ok="refresh" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { roleDelete, rolePage } from '@/api/api'
import type { menuItem } from '@/components/tools/moreIcon.vue'
import useListLoading from '@/hooks/useListLoading'
import { message, Modal } from 'ant-design-vue'
import type { ColumnsType } from 'ant-design-vue/es/table'
import type { RoleType } from '~/types/api/role/common'
import type { RolePageReqType } from '~/types/api/role/page'
import RoleModal from './roleModal.vue'
import { ref } from 'vue'
import type { MenuInfo } from 'ant-design-vue/es/menu/src/interface'
import MoreIcon from '@/components/tools/moreIcon.vue'

const formData = ref<RolePageReqType>({
  keyword: ''
})
const roleModalRef = ref<InstanceType<typeof RoleModal>>()

const { dataList, refresh, getData, pageParams, loading } = useListLoading(rolePage, formData)
const columns: ColumnsType = [
  { title: '角色名称', dataIndex: 'roleName' },
  { title: '用户数量', dataIndex: 'userNum' },
  { title: '', dataIndex: 'actions', width: '64px' }
]

function handlerSizeChange(pageNo: number, pageSize: number) {
  pageParams.value.current = pageNo
  pageParams.value.pageSize = pageSize
  getData()
}

// 更多按钮
const menuList: menuItem[] = [
  { title: '编辑', key: 'edit' },
  { title: '删除', key: 'delete' }
]

// 更多按钮点击事件
function handlerMoreIconClick(clickItem: MenuInfo, record: RoleType) {
  switch (clickItem.key) {
    case 'edit':
      roleModalRef.value?.onOpen({
        title: '编辑',
        record: {
          id: record.id,
          roleName: record.roleName,
          permissionIds: record.permissionIds
        }
      })
      break
    case 'delete':
      if (['超级管理员'].includes(record.roleName)) {
        message.error('该角色为系统默认角色，无法删除')
        return
      }
      if (record.userNum !== 0) {
        message.error('该角色下有用户，无法删除')
        return
      }
      Modal.confirm({
        title: '确定删除该专题吗？',
        content: '删除后不可恢复',
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            loading.value = true
            const { message: msg } = await roleDelete({ id: record.id! })
            message.success(msg)
            await getData()
            loading.value = false
          } catch (error) {
            console.error(error)
            loading.value = false
          }
        }
      })

      break

    default:
      break
  }
}
</script>

<style scoped></style>
