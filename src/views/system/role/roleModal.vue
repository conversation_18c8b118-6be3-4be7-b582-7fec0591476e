<template>
  <a-modal
    v-model:open="visible"
    :title="`${modalTitle}用户`"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="close"
    forceRender
  >
    <a-spin :spinning="confirmLoading">
      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :labelCol="{ span: 5 }"
        :wrapperCol="{ span: 19 }"
        class="userModal"
      >
        <a-form-item label="角色名称" name="roleName">
          <a-input v-model:value="form.roleName" placeholder="角色名称" />
        </a-form-item>
        <a-form-item label="权限点" name="permissionIds">
          <a-tree-select
            ref="treeSelectRef"
            v-model:value="form.permissionIds"
            :treeData="permissionList"
            placeholder="权限点"
            :treeDefaultExpandAll="true"
            :multiple="true"
            treeCheckable
            :fieldNames="{ children: 'subList', label: 'permissionName', value: 'id' }"
            @change="handleChange"
            :showCheckedStrategy="TreeSelect.SHOW_ALL"
          ></a-tree-select>
          <!-- treeCheckStrictly -->
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { roleCreate, rolePermissionList, roleUpdate } from '@/api/api'
import useRequest from '@/hooks/useRequest'
import { message, TreeSelect } from 'ant-design-vue'
import type { FormExpose } from 'ant-design-vue/es/form/Form'
import { isUndefined } from 'lodash-es'
import { ref } from 'vue'
import type { CreatedRoleType, UpdateRoleType } from '~/types/api/role/common'
import type { RolePermissionType } from '~/types/api/role/permissionList'

type FormType = CreatedRoleType | UpdateRoleType

const emit = defineEmits(['ok', 'close'])
const formRef = ref<FormExpose>()
const modalTitle = ref('')
const confirmLoading = ref(false)
const visible = ref(false)
const form = ref<FormType>({
  id: undefined,
  roleName: '',
  permissionIds: []
})
const rules = {
  userName: [{ required: true, message: '请输入' }],
  roleIds: [{ required: true, message: '请选择' }]
}

const { dataList: permissionList } = useRequest(rolePermissionList)
// const permissionList = [
//   {
//     label: '1',
//     value: 1,
//     children: [
//       { label: '1-1', value: 1.1 },
//       { label: '1-2', value: 1.2 }
//     ]
//   },
//   {
//     label: '2',
//     value: 2,
//     children: [
//       { label: '2-1', value: 2.1 },
//       { label: '2-2', value: 2.2 }
//     ]
//   }
// ]

function onOpen(openData: { title: '新增' | '编辑'; record?: FormType }) {
  console.log('openData: ', openData)
  modalTitle.value = openData.title
  const record = openData.record
  console.log('record: ', record)

  if (openData.title === '编辑' && !isUndefined(record)) {
    const { checkedIds, halfCheckedIds } = getCheckedStatus(permissionList.value!, record?.permissionIds)
    console.log('checkedIds: ', checkedIds)
    console.log('halfCheckedIds: ', halfCheckedIds)
    form.value = {
      ...record,
      permissionIds: checkedIds
    }
  }
  console.log('form.value: ', form.value)

  visible.value = true
}

async function handleOk() {
  // 触发表单验证
  try {
    await formRef.value?.validateFields()
    let reqApi = modalTitle.value === '新增' ? roleCreate : roleUpdate
    console.log('form.value: ', form.value)
    confirmLoading.value = true
    const params = {
      ...form.value,
      permissionIds: [...form.value.permissionIds, ...halfChecked]
    }
    const { message: msg } = await reqApi(params)
    message.success(msg)
    emit('ok')
    confirmLoading.value = false
    close()
  } catch (error) {
    console.error(error)
    confirmLoading.value = false
  }
}

function close() {
  emit('close')
  formRef.value?.resetFields()
  visible.value = false
  form.value = {
    id: undefined,
    roleName: '',
    permissionIds: []
  }
}

let halfChecked: string[] = []
function handleChange(val: string[]) {
  const { halfCheckedIds } = getCheckedStatus(permissionList.value!, val)
  halfChecked = halfCheckedIds
}

// 获取选中状态和半选中状态
function getCheckedStatus(treeNodes: RolePermissionType[], selectedIds: string[]) {
  const halfCheckedIds: string[] = []
  const checkedIds: string[] = []

  function checkNode(node: RolePermissionType) {
    const isSelected = selectedIds.includes(node.id)
    const hasChildren = node.subList && node.subList.length > 0

    if (!hasChildren) {
      if (isSelected) {
        checkedIds.push(node.id)
      }
      return { checked: isSelected, halfChecked: false }
    }

    const childStatuses = node.subList.map(child => checkNode(child))
    const allChildrenChecked = childStatuses.every(c => c.checked)
    const someChildrenChecked = childStatuses.some(c => c.checked || c.halfChecked)

    let currentChecked = false
    let currentHalfChecked = false

    if (isSelected) {
      if (allChildrenChecked) {
        currentChecked = true
      } else {
        currentHalfChecked = true
      }
    } else {
      if (someChildrenChecked) {
        currentHalfChecked = true
      }
    }

    if (currentChecked) {
      checkedIds.push(node.id)
    } else if (currentHalfChecked) {
      halfCheckedIds.push(node.id)
    }

    return { checked: currentChecked, halfChecked: currentHalfChecked }
  }

  treeNodes.forEach(rootNode => checkNode(rootNode))

  return { halfCheckedIds, checkedIds }
}

defineExpose({
  onOpen
})
</script>

<style scoped></style>
