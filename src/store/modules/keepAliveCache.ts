/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-11-07 14:39:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-19 16:04:30
 * @FilePath: /global-intelligence-web/src/store/modules/keepAliveCache.ts
 * @Description:
 */
import { defineStore } from 'pinia'
import qs from 'qs'
import type { ConcreteComponent, VNode } from 'vue'
import type { RouteLocationNormalizedLoaded } from 'vue-router'

type CacheKey = string | number | symbol | ConcreteComponent

export const wrapperMap = new Map<CacheKey, { name: string; render(): VNode }>() // 不能放在state中，否则会警告，
export const getRouteKey = (route: RouteLocationNormalizedLoaded): string => {
  const _route = route.matched.length !== 3 ? route.matched[2] : route // 判断当前路由层级，因为4层路由的情况下，route对象会拿到最后一层的router信息，导致key不能复用
  const key = `${_route?.path}?${qs.stringify(route?.query)}` // 当前路由的唯一key
  return key
}

export const useKeepAliveCache = defineStore('keepAliveCache', {
  state: () => {
    return {
      cachedViews: <string[]>[]
    }
  },
  getters: {
    getCachedViews: state => state.cachedViews
  },
  actions: {
    // 新增缓存路由
    addCachedView(routerKey: string) {
      // 没有缓存过的都加入缓存
      if (!this.cachedViews.includes(routerKey)) {
        this.cachedViews.push(routerKey)
      }
    },
    // 删除指定缓存路由
    delCachedView(router: RouteLocationNormalizedLoaded) {
      const routerKey = getRouteKey(router)
      const index = this.cachedViews.indexOf(routerKey)
      if (index > -1) {
        this.cachedViews.splice(index, 1)
      }

      if (wrapperMap.has(routerKey)) {
        wrapperMap.delete(routerKey)
      }
    },
    // 删除所有缓存路由
    delAllCachedViews() {
      this.cachedViews = []
      wrapperMap.clear()
    },
    // 删除除了这个路由以外所有路由
    delOthersCachedViews(router: RouteLocationNormalizedLoaded) {
      const routerKey = getRouteKey(router)
      console.log('delOthersCachedViews routerKey', routerKey)
      const index = this.cachedViews.indexOf(routerKey)
      if (index > -1) {
        this.cachedViews = this.cachedViews.slice(index, index + 1)
      } else {
        this.cachedViews = []
      }
      wrapperMap.forEach((_value, key) => {
        if (routerKey !== key) {
          wrapperMap.delete(key)
        }
      })
    }
  }
})
