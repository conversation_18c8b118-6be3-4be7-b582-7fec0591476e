/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-12 17:14:45
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-19 15:26:01
 * @FilePath: /global-intelligence-web/src/store/modules/user.ts
 * @Description:
 */
import { defineStore } from 'pinia'
import { getLocal, setLocal, removeLocal, getToken, setToken, removeToken } from '@/utils/storage'
import { usePermissionStore } from './permission'
import { asyncRouter as NON_AUTH_ROUTE } from '@/router/routes'
import type { SysUserLoginVo } from '~/types/api/sys/login'
import { isEmpty, isUndefined } from 'lodash-es'
import { sysUserPermission } from '@/api/api'
import type { SysMenuVo } from '~/types/api/sys/permission'
import type { RouterType } from '~/types/router'

// interface UserInfo {
//   id: number
//   name: string
//   email: string
//   // 你可以根据需要添加更多的字段
// }

interface UserState {
  token?: string
  userInfo?: SysUserLoginVo
}

function menuListToRouterType(list: SysMenuVo[]): RouterType[] {
  const n = []
  for (let index = 0; index < list.length; index++) {
    const element = list[index]
    const rawData = JSON.parse(element.extendParam)
    n.push({
      name: element.name,
      path: element.path,
      component: rawData.component,
      meta: rawData.meta,
      redirect: rawData.redirect,
      children: element.children ? menuListToRouterType(element.children) : []
    })
  }
  return n
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    token: undefined,
    userInfo: undefined
  }),
  getters: {
    getToken(): string | undefined {
      return this.token
    },
    // 获取用户信息
    getUserInfo(): SysUserLoginVo | undefined {
      return this.userInfo
    }
  },
  actions: {
    // 登录方法
    login(token: string, userInfo: SysUserLoginVo) {
      return new Promise<void>(resolve => {
        this.token = token
        this.userInfo = userInfo
        // 你可以在这里将 token 和 userInfo 存储到 localStorage 或其他地方
        setToken(token)
        setLocal('userInfo', userInfo)
        resolve()
      })
    },
    // 注销方法
    logout() {
      return new Promise<void>(resolve => {
        this.token = undefined
        this.userInfo = undefined
        // 清除存储的 token 和 userInfo
        removeToken()
        removeLocal('userInfo')
        resolve()
      })
    },
    // 从 localStorage 加载 token 和 userInfo
    loadFromLocalStorage() {
      const token = getToken()
      const userInfo = getLocal<SysUserLoginVo>('userInfo')
      if (token && userInfo) {
        this.token = token
        this.userInfo = userInfo
      }
    },
    // 获取权限点
    async fetchPermissionList() {
      try {
        const permissionStore = usePermissionStore()
        const token = this.token || getToken()
        let asyncRouter = NON_AUTH_ROUTE
        // 只有线上环境才会使用后端加载的路由
        if (!isEmpty(token)) {
          const { result } = await sysUserPermission()
          const { menuList } = result
          if (!isUndefined(menuList) && import.meta.env.MODE === 'production') {
            asyncRouter = menuListToRouterType(menuList)
          }
        }
        // 加载路由
        if (asyncRouter && asyncRouter.length > 0) {
          const addRouter = await permissionStore.updateRouterList(asyncRouter)
          return Promise.resolve(addRouter)
        } else {
          return Promise.reject('getPermissionList：权限必须是非空数组！')
        }
      } catch (error) {
        return Promise.reject(error)
      }
    }
  }
})
