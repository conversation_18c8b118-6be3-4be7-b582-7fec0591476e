/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-31 16:25:45
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-31 16:41:02
 * @FilePath: /global-intelligence-web/src/store/modules/tag.ts
 * @Description:
 */
import { getSystemLabel } from '@/api/api'
import useRequest from '@/hooks/useRequest'
import { defineStore } from 'pinia'

export const useTagStore = defineStore('tag', () => {
  const { dataList: labelMap } = useRequest(getSystemLabel, { sceneType: 'news' })

  function getTag(key: string) {
    return labelMap.value?.[key] || []
  }

  return { getTag, labelMap }
})
