/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-11 16:07:44
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-18 19:52:15
 * @FilePath: /global-intelligence-web/src/store/modules/permission.ts
 * @Description:
 */
import { defineStore } from 'pinia'
import { basicRouterMap } from '@/router/routes'
import type { RouteRecordRaw } from 'vue-router'
import type { RouterType } from '~/types/router'
import { convertRouteItem, convertToRouteRecord } from '@/router/utils'

export const usePermissionStore = defineStore('permission', {
  state: () => {
    return {
      routers: basicRouterMap,
      addRouters: <RouteRecordRaw[]>[],
      permissionList: <RouteRecordRaw[]>[]
      // activationSecondRouter: <RouterType>{}
    }
  },
  getters: {
    getPermissionList: state => state.permissionList
    // getActivationSecondRouter: state => state.activationSecondRouter
  },
  actions: {
    // 动态添加主界面路由，需要缓存
    async updateRouterList(addRouter: RouterType[]): Promise<RouteRecordRaw[]> {
      try {
        this.permissionList = addRouter.map(convertRouteItem) // 设置路由
        const asyncRoutes = convertToRouteRecord(addRouter)
        this.addRouters = asyncRoutes
        this.routers = basicRouterMap.concat(asyncRoutes)
        return asyncRoutes
      } catch (error) {
        console.error(error)
        throw error
      }
    }
  }
})
