/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-10 16:11:53
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-09-12 15:53:09
 * @FilePath: /corp-elf-web-consumer/src/store/index.ts
 * @Description:
 */
import { createPinia } from 'pinia'
import type { App } from 'vue'

export function setupStore(app: App) {
  app.use(createPinia())
}

export * from './modules/user'
export * from './modules/permission'
export * from './modules/keepAliveCache'
export * from './modules/tag'
