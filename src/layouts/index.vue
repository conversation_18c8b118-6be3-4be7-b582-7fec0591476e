<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-09 18:02:56
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-05-15 17:27:02
 * @FilePath: /global-intelligence-web/src/layouts/index.vue
 * @Description: 
-->
<template>
  <a-layout class="relative z-1 bg-#f3f3f3">
    <sideMenu />
    <a-layout-content class="ml92px">
      <!-- class="scrollLock" -->
      <div class="h-100vh bg-#fff">
        <router-view />
      </div>
    </a-layout-content>
  </a-layout>
</template>

<script setup lang="ts" name="globalLayout">
import sideMenu from './sideMenu.vue'
</script>

<style lang="less" scoped>
.scrollLock {
  padding-left: calc(100vw - 100%);
}
</style>
