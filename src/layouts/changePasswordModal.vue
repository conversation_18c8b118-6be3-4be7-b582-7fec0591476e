<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-18 17:35:47
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-04-07 10:23:08
 * @FilePath: /global-intelligence-web/src/layouts/changePasswordModal.vue
 * @Description: 
-->
<template>
  <a-modal v-model:open="visible" title="重置密码" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="close">
    <a-spin :spinning="confirmLoading">
      <a-form ref="formRef" :model="form" :rules="rules" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
        <a-form-item label="旧密码" name="oldPassword">
          <a-input-password autoComplete="new-password" v-model:value="form.oldPassword" placeholder="旧密码" />
        </a-form-item>
        <a-form-item label="密码" name="newPassword">
          <a-input-password autoComplete="new-password" v-model:value="form.newPassword" placeholder="密码" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts" name="userModal">
import { ref } from 'vue'
import { userModifyPassword } from '@/api/api'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'

const emit = defineEmits(['ok', 'close'])
const formRef = ref()
const confirmLoading = ref(false)
const visible = ref(false)
const form = ref({
  oldPassword: '',
  newPassword: ''
})
const rules: Record<string, Rule[]> = {
  oldPassword: [{ required: true, message: '请输入登录密码!' }],
  newPassword: [
    { required: true, message: '请输入登录密码!' },
    {
      pattern: /^\S*(?=\S{8,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*?])\S*$/,
      message: '密码必须包含大写字母、小写字母、数字和特殊字符，且长度不小于8位'
    }
  ]
}

function onOpen() {
  visible.value = true
}

async function handleOk() {
  // 触发表单验证
  try {
    await formRef.value.validateFields()
    console.log('form.value: ', form.value)
    confirmLoading.value = true
    const { message: msg } = await userModifyPassword(form.value)
    message.success(msg)
    emit('ok')
    confirmLoading.value = false
    close()
  } catch (error) {
    console.error(error)
    confirmLoading.value = false
  }
}

function close() {
  emit('close')
  formRef.value.resetFields()
  visible.value = false
  form.value = {
    oldPassword: '',
    newPassword: ''
  }
}

defineExpose({
  onOpen
})
</script>

<style lang="less" scoped></style>
