<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-06-27 15:41:00
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-18 14:02:20
 * @FilePath: /global-intelligence-web/src/layouts/sideMenu.vue
 * @Description:
-->
<template>
  <a-layout-sider
    width="92px"
    theme="light"
    class="bg-#fff border-c-#050505-6 border-r-1px border-solid fixed! left-0 top-0 z-9 h-100%"
  >
    <div class="h100% w100% flex flex-direction-column">
      <div class="py16px h100% flex flex-direction-column">
        <div>
          <router-link to="/">
            <div class="m-0 mx-auto w48px h48px br-16px cursor-pointer flex-center-center bg-#6553ee">
              <img src="@/assets/logo_white.svg" mode="scaleToFill" class="w-25px" />
            </div>
          </router-link>
        </div>

        <ul class="my-16px text-center flex-1 overflow-auto">
          <a-space :size="16" direction="vertical" align="center">
            <li v-for="(menuItem, index) in menus" :key="index" :name="menuItem.name">
              <!-- 聚合显示 -->
              <div v-if="menuItem.meta?.aggregate || menuItem.children?.length === 1">
                <router-link
                  @click="handleRouterClick(menuItem)"
                  :to="menuItem.path"
                  :class="['navItem', firstMenuKey === menuItem.name ? 'activeNavItem' : '']"
                >
                  <div class="icon">
                    <iconfontIcon
                      v-if="menuItem.meta?.icon"
                      :icon="menuItem.meta?.icon"
                      :extraCommonProps="{ style: { fontSize: '30px' } }"
                    />
                    <span v-else :style="{ fontSize: '24px' }">{{ (menuItem.meta!.title as string)[0] }}</span>
                  </div>
                  <p class="mt-4px navLabel">
                    {{ menuItem.meta!.title }}
                  </p>
                </router-link>
              </div>
              <!-- 显示子菜单 -->
              <a-popover
                v-else
                placement="rightTop"
                trigger="hover"
                :overlayInnerStyle="{ padding: 0, overflow: 'hidden' }"
              >
                <template #content>
                  <template v-for="(item, index) in menuItem.children" :key="index">
                    <router-link :to="item.path" class="color-#333">
                      <div class="white-space-nowrap p12px hoverPrimaryColor">
                        {{ item.meta!.title }}
                      </div>
                    </router-link>
                    <div class="px-8px">
                      <a-divider v-if="index !== menuItem.children!.length - 1" style="margin: 0px" />
                    </div>
                  </template>
                </template>
                <span :class="['navItem', firstMenuKey === menuItem.name ? 'activeNavItem' : '']">
                  <div class="icon">
                    <iconfontIcon v-if="menuItem.meta?.icon" :icon="menuItem.meta?.icon" class="fs-30px" />
                    <span v-else class="fs-24px">
                      {{ (menuItem.meta!.title as string)[0] }}
                    </span>
                  </div>

                  <p class="mt-4px navLabel">{{ menuItem.meta!.title }}</p>
                </span>
              </a-popover>
            </li>
          </a-space>
        </ul>

        <div class="pb-16px flex-center-center">
          <a-popover
            placement="rightTop"
            trigger="hover"
            :overlayInnerStyle="{ padding: 0, overflow: 'hidden' }"
            arrowPointAtCenter
          >
            <template #content>
              <div class="white-space-nowrap">
                <p class="hoverPrimaryColor p12px" @click="resetPasswordModalRef?.onOpen()">重置密码</p>
                <div class="px-8px">
                  <a-divider style="margin: 0px" />
                </div>
                <p class="hoverPrimaryColor p12px" @click="logout">注销</p>
              </div>
            </template>

            <span>
              <a-avatar :size="48" style="color: #fff; background-color: #6553ee">
                {{ userStore.getUserInfo?.username[0].toLocaleUpperCase() }}
                <!-- <template #icon><UserOutlined /></template> -->
              </a-avatar>
            </span>
          </a-popover>
        </div>
      </div>
    </div>
    <ChangePasswordModal ref="changePasswordRef" />
    <ResetPasswordModal ref="resetPasswordModalRef" />
  </a-layout-sider>
</template>

<script setup lang="ts">
import { useKeepAliveCache, usePermissionStore, useUserStore } from '@/store'
import { useRoute, type RouteRecordRaw } from 'vue-router'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { sysLogout } from '@/api/api'
import { ref, watch, onMounted, h, useTemplateRef } from 'vue'
import { Modal } from 'ant-design-vue'
import ChangePasswordModal from './changePasswordModal.vue'
import ResetPasswordModal from './resetPasswordModal.vue'

const changePasswordRef = useTemplateRef('changePasswordRef')
const resetPasswordModalRef = useTemplateRef('resetPasswordModalRef')
const permissionStore = usePermissionStore()

const menus = permissionStore.permissionList.filter(item => !item.meta?.hidden) // 过滤隐藏的
console.log('menus: ', menus)

const firstMenuKey = ref('dashboard')
const route = useRoute()

watch(() => route.path, setFirstMenuKey)

onMounted(() => {
  setFirstMenuKey()
})

// function handlerMenuItemClick(menuItem: RouteRecordRaw) {
//   console.log('menuItem: ', menuItem)

//   router
//     .push({
//       // name: menuItem.name,
//       path: menuItem.path
//     })
//     .then(() => {
//       keepAliveCache.delOthersCachedViews(route)
//     })
// }

function setFirstMenuKey() {
  // 递归查找对应的路由
  function deepFor(routerList: RouteRecordRaw[], key: string) {
    function findRouter(arr: RouteRecordRaw[]) {
      for (let index = 0; index < arr.length; index++) {
        const routerItem = arr[index]
        if (routerItem.name === key) {
          return routerItem
        }

        if (routerItem.children && routerItem.children.length !== 0 && findRouter(routerItem.children)) {
          return routerItem
        }
      }
    }

    return findRouter(routerList)
  }

  const routerList = permissionStore.permissionList
  const actionRouter = deepFor(routerList, route.name as string)
  firstMenuKey.value = actionRouter?.name as string
}

const userStore = useUserStore()
function logout() {
  Modal.confirm({
    title: '提示',
    content: '真的要退出登录吗 ?',
    icon: h(iconfontIcon, { icon: 'icon-info-circle', style: 'font-size: 24px' }),
    autoFocusButton: null,
    onOk() {
      sysLogout()
        .then(() => userStore.logout())
        .then(() => {
          window.location.reload()
        })
    }
  })
}

const keepAliveCacheStore = useKeepAliveCache()
function handleRouterClick(item) {
  keepAliveCacheStore.delAllCachedViews()
}
</script>

<style lang="less" scoped>
.navItem {
  @apply transition-all;
  color: rgba(0, 0, 0, 0.45);

  &:hover {
    color: #6553ee;
    .icon {
      color: #6553ee;
      background-color: #f4f0ff;
    }
    .navLabel {
      color: #6553ee;
    }
  }
  .icon {
    border-radius: 16px;
    width: 52px;
    height: 52px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .navLabel {
    color: rgba(0, 0, 0, 0.65);
  }
}

.activeNavItem {
  color: #6553ee;
  .icon {
    background-color: #f4f0ff;
  }
  .navLabel {
    color: #6553ee;
  }
}
</style>
