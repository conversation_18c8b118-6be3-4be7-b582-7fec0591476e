<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-09 16:54:50
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-05-22 16:53:22
 * @FilePath: /global-intelligence-web/src/layouts/keepAliveLayout.vue
 * @Description: 
-->
<template>
  <router-view v-slot="{ Component, route }">
    <transition name="fade-slide" mode="out-in" appear>
      <keep-alive :include="cachedViews">
        <component :is="wrap(route, Component)" :key="routeKey" />
      </keep-alive>
    </transition>
  </router-view>
</template>

<script setup lang="ts">
import { useKeepAliveCache } from '@/store'
import { computed, h, onMounted, type VNode, watch } from 'vue'
import { type RouteLocationNormalizedLoaded, RouterView, useRoute } from 'vue-router'
import { getRouteKey, wrapperMap } from '@/store/modules/keepAliveCache'

const route = useRoute()
const keepAliveCache = useKeepAliveCache()

const cachedViews = computed(() => keepAliveCache.getCachedViews) // 所有缓存路由
const routeKey = computed(() => getRouteKey(route)) // 获取当前路由的唯一key

// 监控router变化，插入cache
watch(
  () => route.fullPath,
  (_newVal, _oldVal) => {
    // route.meta
    const isHave = keepAliveCache.getCachedViews.includes(_newVal)
    const { keepAlive } = route.meta
    console.log('keepAlive: ', keepAlive);
    console.log('isHave: ', isHave);
    if (!isHave && keepAlive) {
      keepAliveCache.addCachedView(routeKey.value)
    }
  },
  { immediate: true }
)

// 参考https://github.com/vuejs/core/pull/4339#issuecomment-1238984279
// 为keep-alive里的component接收的组件包上一层自定义name的壳.
function wrap(_route: RouteLocationNormalizedLoaded, component: VNode) {
  let wrapper: { name: string; render(): VNode }
  // 重点就是这里，这个组件的名字是完全可控的，
  // 只要自己写好逻辑，每次能找到对应的外壳组件就行，完全可以写成任何自己想要的名字.
  // 这就能配合 keep-alive 的 include 属性可控地操作缓存.
  if (wrapperMap.has(routeKey.value)) {
    wrapper = wrapperMap.get(routeKey.value)!
  } else {
    wrapper = {
      name: routeKey.value,
      render() {
        return h('div', { class: ['vaf-page-wrapper w100% h100%'] }, component)
      }
    }
    wrapperMap.set(routeKey.value, wrapper)
  }
  return h(wrapper)
}

onMounted(() => {
  // keepAliveCache.addCachedView(routeKey.value)
})
</script>

<style lang="less" scoped>
.main {
  // padding: 16px 0;
  height: 100%;
}
</style>
