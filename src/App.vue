<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-02-26 14:57:11
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-05-07 20:04:50
 * @FilePath: /global-intelligence-web/src/App.vue
 * @Description: 
-->
<template>
  <a-config-provider
    :locale="locale"
    virtual
    :getPopupContainer="getPopupContainer"
    :input="{ autocomplete: 'off' }"
    :theme="antdTheme"
  >
    <router-view />
  </a-config-provider>
</template>

<script setup lang="ts">
import locale from 'ant-design-vue/es/locale/zh_CN'
import type { ThemeConfig } from 'ant-design-vue/es/config-provider/context'
import { useUserStore } from './store'
import { onMounted } from 'vue'
import { getLocal, getToken } from './utils/storage'
import { isEmpty } from 'lodash-es'
import type { SysUserLoginVo } from '~/types/api/sys/login'
const userStore = useUserStore()

function getPopupContainer(_triggerNode: HTMLElement, _dialogContext: HTMLElement) {
  if (_triggerNode) {
    return _triggerNode.parentNode
  } else {
    return document.body
  }
}

const antdTheme: ThemeConfig = {
  token: { colorPrimary: '#6553ee', colorInfo: '#6553ee' },
  components: {
    Typography: {
      sizeMarginHeadingVerticalEnd: 0
    },
    Layout: {
      // colorBgHeader: '#fff',
      // colorBgBody: '#fff'
    },
    Form: { marginLG: 16 },
    Card: { paddingLG: 16, fontSizeLG: 18 },
    Modal: { marginXS: 20, marginSM: 16 },
    // Tag: { colorFillAlter: '#eae7fb' },
    Table: { colorFillAlter: 'rgba(152, 133, 237, 0.06)' },
    Alert: { paddingContentHorizontalLG: 16 },
    List: { paddingContentVertical: 16, paddingContentHorizontalLG: 16 },
    Result: { paddingLG: 12 }
  }
}

onMounted(() => {
  const token = getToken() as string
  const userInfo = getLocal<SysUserLoginVo>('userInfo')
  if (!isEmpty(token) && !isEmpty(userInfo)) {
    userStore.login(token, userInfo)
  }
})
</script>

<style scoped></style>
