#app {
  width: 100%;
  height: 100%;
  min-width: 1400px;
}

:root {
  --g-primary-color: #6553ee;
  --g-primary-hover-color: #baabff;
  --g-secondary-color: #fccb3a;
}

.loadMore {
  text-align: center;
  .endText {
    text-align: center;
    height: 44px;
    line-height: 44px;
    color: #909399;
  }
}

@scrollbarColor: #e8e8e8;
@scrollbarColorHover: #d8d8d8;
@scrollbarColorActive: #c8c8c8;
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  transition: 0.25s;
}
::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 100px;
}
&:hover {
  &::-webkit-scrollbar-thumb {
    background-color: @scrollbarColor;
    &:active {
      background-color: @scrollbarColorActive;
    }
    &:hover {
      background-color: @scrollbarColorHover;
    }
  }
}

.hoverPrimaryColor {
  &:hover {
    cursor: pointer;
    color: var(--g-primary-hover-color);
  }
}
