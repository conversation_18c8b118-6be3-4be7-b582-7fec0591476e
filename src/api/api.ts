/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-02-26 17:33:37
 * @LastEditors: 黄宏智(<PERSON><PERSON>)
 * @LastEditTime: 2025-07-18 15:06:34
 * @FilePath: /global-intelligence-web/src/api/api.ts
 * @Description:
 */
import { getAction, postAction } from './manage'
import type { paginationBaseRequest, paginationBaseResponse } from '~/types/pagination'
import type { newsPageReqType, newsPageResType } from '~/types/api/news/page.d'
import type { getSystemLabelResType, getSystemLabelReqType } from '~/types/api/system/getSystemLabel.d'
import type { websitePageReqType, websitePageResType, WebsiteVo } from '~/types/api/website/page.d'
import type { NewsTopicSubscribeTopicResType } from '~/types/api/newsTopic/subscribe.d'
import type { CreatedNewsTopicType, UpdateNewsTopicType } from '~/types/api/newsTopic/common.d'
import type { NewsTopicPageReqType, NewsTopicPageResType } from '~/types/api/newsTopic/page.d'
import type { CreatedUserType, UpdateUserType } from '~/types/api/user/common'
import type { UserPageReqType, UserPageResType } from '~/types/api/user/page.d'
import type { RolePageReqType, RolePageResType } from '~/types/api/role/page.d'
import type { UpdateRoleType, CreatedRoleType } from '~/types/api/role/common.d'
import type { SysUserPermissionResType } from '~/types/api/sys/permission.d'
import type { SysLoginReqType, SysLoginResType } from '~/types/api/sys/login.d'
import type { RolePermissionResType } from '~/types/api/role/permissionList.d'
import type {
  economicIndicatorPageReqType,
  economicIndicatorPageResType
} from '~/types/api/data/economicIndicatorPage.d'
import type {
  economicIndicatorChangePageReqType,
  economicIndicatorChangePageResType
} from '~/types/api/data/economicIndicatorChangePage.d'
import type { futuresMarketPageReqType, futuresMarketPageResType } from '~/types/api/data/futuresMarketPage.d'
import type { foreignExchangeLastReqType, foreignExchangeLastResType } from '~/types/api/data/foreignExchangeLast.d'
import type {
  foreignExchangeHistoryReqType,
  foreignExchangeHistoryResType
} from '~/types/api/data/foreignExchangeHistory.d'
import type { regionalDynamicsReqType, regionalDynamicsResType } from '~/types/api/informationBoard/regionalDynamics'
import type { governmentNoticeReqType, governmentNoticeResType } from '~/types/api/informationBoard/governmentNotice'
import type { industryHostResType } from '~/types/api/informationBoard/industryHost'
import type { competitiveTrendsReqType, competitiveTrendsResType } from '~/types/api/informationBoard/competitiveTrends'
import type { foreignExchangeResType } from '~/types/api/informationBoard/foreignExchange'
import type { commodityPriceResType } from '~/types/api/informationBoard/commodityPrice'
import type { riskImpactResType, riskImpactReqType } from '~/types/api/riskBoard/riskImpact.d'
import type { riskInfoResType } from '~/types/api/riskBoard/riskInfo.d'
import type { riskSubTypeTopResType } from '~/types/api/riskBoard/riskSubTypeTop.d'
import type { effectiveRiskResType } from '~/types/api/riskBoard/effectiveRisk.d'
import type { AgentMessagesResType } from '~/types/api/agent/messages.d'
import type { AgentConversationsResType } from '~/types/api/agent/conversations.d'
import type { ChatAgentReqType } from '~/types/api/agent/common.d'

// 新闻模块
export const newsPage = (params: newsPageReqType) =>
  getAction<paginationBaseResponse<newsPageResType>>(`/news/page`, params) // 首页->保存最近浏览
export const newsRead = (params: { dataId: string }) => getAction(`/news/read`, params) // 标记已读
export const newsReadAllTopicNews = (params: { topicId: string }) => getAction(`/news/readAllTopicNews`, params) // 全部新闻标记为已读

// 码表查询
export const getSystemLabel = (params: getSystemLabelReqType) =>
  getAction<getSystemLabelResType>(`/system/label/querySceneSystemLabel`, params) // 首页->保存最近浏览
// 站点管理
export const websitePage = (params: websitePageReqType) => getAction<websitePageResType>(`/website/page`, params) // 获取站点列表
export const websiteTopic = (topicId: string) => getAction<WebsiteVo[]>(`/website/topic`, { topicId }) // 根据订阅专题id查询勾选的站点
// 专题管理
export const newsTopicPage = (params: NewsTopicPageReqType) =>
  getAction<NewsTopicPageResType>(`/news/topic/page`, params) // 分页查询
export const newsTopicCreate = (params: CreatedNewsTopicType) => postAction(`/news/topic/create`, params) // 创建专题
export const newsTopicUpdate = (params: UpdateNewsTopicType) => postAction(`/news/topic/update`, params) // 更新专题
export const newsTopicDelete = (params: { id: string }) => getAction(`/news/topic/delete`, params) // 删除专题
export const newsTopicSubscribe = () => getAction<NewsTopicSubscribeTopicResType[]>(`/news/topic/subscribe`) // 我的订阅-不分页

// 用户登录
export const sysLogin = (params: SysLoginReqType) => postAction<SysLoginResType>(`/sys/login`, params) // 登录
export const sysLogout = () => getAction(`/sys/logout`) // 注销
export const sysUserPermission = () => getAction<SysUserPermissionResType>(`/sys/user/permission`) // 获取权限点

// 系统角色管理
export const rolePage = (params: RolePageReqType) => getAction<RolePageResType>('/role/page', params) // 分页查询
export const roleCreate = (params: CreatedRoleType) => postAction('/role/create', params) // 新增
export const roleUpdate = (params: UpdateRoleType) => postAction('/role/update', params) // 编辑
export const roleDelete = (params: { id: string }) => getAction('/role/delete', params) // 删除
export const rolePermissionList = () => getAction<RolePermissionResType>('/role/permission/list') // 权限点

// 系统用户管理
export const userPage = (params: UserPageReqType) => getAction<UserPageResType>('/user/page', params) // 分页查询
export const userCreate = (params: CreatedUserType) => postAction('/user/create', params) // 新增
export const userUpdate = (params: UpdateUserType) => postAction('/user/update', params) // 编辑
export const userDisable = (params: { id: string; disable: boolean }) => getAction('/user/disable', params) // 禁用
export const userResetPassword = (params: { id: string; password: string }) => postAction('/user/resetPassword', params) // 重置密码
export const userDelete = (params: { id: string }) => getAction('/user/delete', params) // 删除
export const userModifyPassword = (params: { oldPassword: string; newPassword: string }) =>
  postAction('/user/modifyPassword', params) // 用户自己修改密码
export const userResetPasswordByUser = (params: { password: string }) => postAction('/user/resetPasswordByUser', params) // 用户自己重置密码

// 经济指标模块
export const dataEconomicIndicatorPage = (params: economicIndicatorPageReqType) =>
  getAction<paginationBaseResponse<economicIndicatorPageResType>>(`/data/economic/indicator/page`, params) // 经济指标
export const dataEconomicIndicatorChangePage = (params: economicIndicatorChangePageReqType) =>
  getAction<paginationBaseResponse<economicIndicatorChangePageResType>>(`/data/economic/indicator/change/page`, params) // 经济指标更新
export const dataFuturesMarketPage = (params: futuresMarketPageReqType) =>
  getAction<paginationBaseResponse<futuresMarketPageResType>>(`/data/futures/market/page`, params) // 商品价格
export const dataForeignExchangeLast = (params: foreignExchangeLastReqType) =>
  getAction<foreignExchangeLastResType>(`/data/foreign/exchange/last`, params) // 外汇
export const dataForeignExchangeHistory = (params: foreignExchangeHistoryReqType) =>
  getAction<foreignExchangeHistoryResType[]>(`/data/foreign/exchange/history`, params) // 外汇历史

// 行业看板
export const informationBoardRegionalDynamics = (params: regionalDynamicsReqType) =>
  getAction<regionalDynamicsResType[]>(`/information/board/regional/dynamics`, params) // 区域动态
export const informationBoardGovernmentNotice = (params: governmentNoticeReqType) =>
  getAction<paginationBaseResponse<governmentNoticeResType>>(`/information/board/government/notice`, params) // 政府公告
export const informationBoardIndustryHost = (params: { industryId: string }) =>
  getAction<industryHostResType[]>(`/information/board/industry/host`, params) // 近30日行业热度
export const informationBoardCompetitiveTrends = (params: competitiveTrendsReqType) =>
  getAction<paginationBaseResponse<competitiveTrendsResType>>(`/information/board/competitive/trends`, params) // 竞对动向
export const informationBoardForeignExchange = () =>
  getAction<foreignExchangeResType[]>(`/information/board/foreign/exchange`) // 外汇
export const informationBoardCommodityPrice = () =>
  getAction<commodityPriceResType[]>(`/information/board/commodity/price`) // 商品价格
export const getNewsDetail = (params: { dataId: string }) => getAction<newsPageResType>(`/news/detail`, params) // 新闻详情

// 风险看板
export const riskBoardEffectiveRisk = () => getAction<effectiveRiskResType[]>(`/risk/board/effective/risk`) // 生效风险
export const riskBoardRiskInfo = (params: paginationBaseRequest) =>
  getAction<paginationBaseResponse<riskInfoResType>>(`/risk/board/risk/info`, params) // 风险信息
export const riskBoardRiskSubTypeTop = () => getAction<riskSubTypeTopResType[]>(`/risk/board/riskSubType/top`) // 近3个月风险类型TOP10
export const riskBoardRiskImpact = (params: riskImpactReqType) =>
  getAction<paginationBaseResponse<riskImpactResType>>(`/risk/board/risk/impact`, params) // 对我方影响

// agent
// export const agentChatMessages = (params: ChatAgentReqType) => postSSEAction('/agent/chat-messages', params) // 开始聊天
export const agentChatMessagesStop = (params: ChatAgentReqType) =>
  postAction<Boolean>('/agent/chat-messages/stop', params) // 结束聊天
export const agentConversations = (params: ChatAgentReqType) =>
  postAction<AgentConversationsResType>('/agent/conversations', params) // 会话列表
export const agentDeleteConversations = (params: ChatAgentReqType) =>
  postAction<Boolean>('/agent/deleteConversations', params) // 删除会话
export const agentMessages = (params: ChatAgentReqType) => postAction<AgentMessagesResType>('/agent/messages', params) // 会话消息
