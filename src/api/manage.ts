/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-11 11:35:08
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-18 17:11:22
 * @FilePath: /global-intelligence-web/src/api/manage.ts
 * @Description:
 */
// import { notification } from 'ant-design-vue'
import type { ResponseData } from '~/types/response'
import axios from './request'
import qs from 'qs'

// get
export async function getAction<T>(url: string, parameter?: Record<string, any>, abortController?: AbortController) {
  return axios.get<ResponseData<T>, ResponseData<T>>(url, {
    params: parameter,
    signal: abortController?.signal,
    paramsSerializer: function (params) {
      return qs.stringify(params, { indices: false })
    }
  })
}

// post
export async function postAction<T>(url: string, parameter: object, abortController?: AbortController) {
  return axios.post<ResponseData<T>, ResponseData<T>>(url, parameter, { signal: abortController?.signal })
}

// sse
export async function getSSEAction(
  url: string,
  parameter: Record<string, string>,
  abortController?: AbortController
): Promise<ReadableStream> {
  return axios.get(url, {
    params: parameter,
    signal: abortController?.signal,
    baseURL: import.meta.env.VITE_BASE_SSE_API,
    adapter: 'fetch',
    responseType: 'stream'
  })
}

// put
export async function putAction<T>(url: string, parameter: object, abortController?: AbortController) {
  return axios.put<ResponseData<T>, ResponseData<T>>(url, parameter, { signal: abortController?.signal })
}

// deleteAction
export async function deleteAction<T>(url: string, parameter: object, abortController?: AbortController) {
  return axios.delete<ResponseData<T>, ResponseData<T>>(url, { params: parameter, signal: abortController?.signal })
}
