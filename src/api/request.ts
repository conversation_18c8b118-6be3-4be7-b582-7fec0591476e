/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-07-30 14:45:33
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-18 17:03:39
 * @FilePath: /global-intelligence-web/src/api/request.ts
 * @Description:
 */
import type { ErrorMessageType, ResponseData } from '~/types/response'
import type { AxiosError, AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import axios from 'axios'
import { notification } from 'ant-design-vue'
import { getToken } from '@/utils/storage'
import { has } from 'lodash-es'

// 白名单接口不参与网关转发和响应拦截器
const WHITE_URL = ['/agent/chat-messages']

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_BASE_API,
  timeout: 1000 * 60 // 请求超时时间
})

const isMockRequest = (config: InternalAxiosRequestConfig) => {
  const { data = '', params, method } = config
  if (
    (method === 'get' && has(params, 'mock') && params.mock) ||
    (method === 'post' && has(data, 'mock') && data.mock)
  ) {
    return true
  }
  return false
}

// 请求拦截-基础方法
const baseRequestInterceptors = {
  onFulfilled: (config: InternalAxiosRequestConfig) => {
    // get请求添加上时间戳
    if (config.method == 'get') {
      config.params = {
        _t: new Date().valueOf(),
        ...config.params
      }
    }

    // 设置Header
    // 将签名和时间戳，添加在请求接口 Header
    // const sign = signMd5Utils.getSign(config.url, config.data)
    const token = getToken() as string
    const mergeHeader: Record<string, string> = { 'PLATFORM-TYPE': 'WEB' }
    if (token) {
      mergeHeader['X-Access-Token'] = token
    }
    config.headers.set(mergeHeader)

    // 如果请求是mock的，需要修改请求地址
    if (isMockRequest(config)) {
      config.baseURL = '/mock'
    }

    return config
  },
  onRejected: (error: AxiosError) => {
    notification.error({ message: '系统提示', description: error.message })
    return Promise.reject(error)
  }
}

// axios请求拦截器
service.interceptors.request.use(baseRequestInterceptors.onFulfilled, baseRequestInterceptors.onRejected)

// axios响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse<any, ResponseData>) => {
    const res = response.data
    const whiteCode = [
      // 正常状态码
      ...[0, 200, 'SUCCESS']
    ]

    // 如果是mock请求，直接返回
    if (response.config.baseURL === '/mock') {
      return {
        code: '200',
        message: '成功',
        result: res.result,
        success: true
      }
    }

    // 白名单接口不参与响应拦截器
    if (WHITE_URL.includes(response.config.url!)) {
      return response.data
    }

    // 根据自定义错误码判断请求是否成功
    if (!whiteCode.includes(res.code)) {
      // if (res.code === 'NON_AUTH') {
      //   // 登录已过期
      //   Modal.error({
      //     title: '登录已过期',
      //     content: res.message,
      //     okText: '重新登录',
      //     mask: false,
      //     onOk: () => {}
      //   })
      // } else {
      // 普通报错提示
      notification.error({ message: '系统提示', description: res.message || res.msg })
      // }
      return Promise.reject(new Error(res.message || res.msg || 'Error'))
    }
    return res
  },
  (err: AxiosError) => {
    // 处理 HTTP 网络错误
    const errorMessage: ErrorMessageType = {
      config: err,
      requestUrl: err.config?.url || '',
      response: undefined,
      message: ''
    }

    if (err.response) {
      const data = err.response.data as ResponseData

      // 定义错误返回数据
      errorMessage.response = data
      errorMessage.message = data.message

      switch (err.response.status) {
        case 403:
          notification.error({ message: '系统提示', description: '拒绝访问', duration: 4 })
          break
        // case 500:
        //   if (token && data.code === 'NON_AUTH') {
        //     Modal.error({
        //       title: '登录已过期',
        //       content: data.message,
        //       okText: '重新登录',
        //       mask: false,
        //       onOk: () => {
        //         userStore.logout()
        //       }
        //     })
        //   }
        //   break
        case 404:
          notification.error({ message: '系统提示', description: '很抱歉，资源未找到!', duration: 4 })
          break
        case 504:
          notification.error({ message: '系统提示', description: '网络超时' })
          break
        case 401:
          notification.error({ message: '系统提示', description: '未授权，请重新登录', duration: 4 })
          break
        case 400:
          notification.error({ message: '系统提示', description: '加载资源失败' })
          // Sentry.captureException(err)
          break
        default:
          // 错误上报
          // Sentry.captureException(err)
          notification.error({
            message: '系统提示',
            description: data.message,
            duration: 4
          })
          break
      }
    }

    return Promise.reject(errorMessage)
  }
)

export default service
