/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-09-14 14:53:48
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-05-22 15:35:08
 * @FilePath: /global-intelligence-web/src/hooks/useListLoading.ts
 * @Description: 分页数据
 */
import type { PaginationConfig } from 'ant-design-vue/es/pagination'
import { isEmpty, isBoolean, isUndefined } from 'lodash-es'
import { computed, isRef, onMounted, ref } from 'vue'
import type { Ref } from 'vue'
import type { paginationBaseResponse } from '~/types/pagination'
import type { ResponseData } from '~/types/response'

interface configType<resType> {
  pageParams?: paginationParamsType
  immediateReqData?: boolean
  /** 对 result 进行额外处理 */
  transformResult?: (result: resType[]) => resType[]
}

interface paginationParamsType
  extends Pick<
    PaginationConfig,
    | 'current'
    | 'pageSize'
    | 'total'
    | 'showSizeChanger'
    | 'hideOnSinglePage'
    | 'pageSizeOptions'
    | 'responsive'
    | 'showTotal'
    | 'onChange'
  > {
  maxPages?: number
}
const useListLoading = <reqType, resType>(
  api: (params: reqType) => Promise<ResponseData<paginationBaseResponse<resType>>>,
  otherParams?: Ref<reqType> | reqType,
  config?: configType<resType>
): {
  loading: Ref<boolean>
  dataList: Ref<resType[]>
  pageParams: Ref<paginationParamsType>
  getData(): Promise<ResponseData<paginationBaseResponse<resType>>>
  refresh(): void
  changePage(): void
} => {
  const immediateReqData = isBoolean(config?.immediateReqData) ? config?.immediateReqData : true
  const pageParams = config?.pageParams || {}
  const transformResult = config?.transformResult // 获取外部传入的 transformResult 函数

  const loading = ref(false)
  const dataList = ref<resType[]>([]) as Ref<resType[]>

  // 分页参数
  const paginationParams = ref<paginationParamsType>({
    current: 1, // 当前页数
    pageSize: 10, // 每页显示条目个数
    total: 0, // 总条目数
    maxPages: 1, // 总条目数
    showSizeChanger: true, // 不显示pagesize修改
    hideOnSinglePage: true, // 只有一页时是否隐藏
    pageSizeOptions: ['10', '30', '50'], // 每页显示个数选择器的选项设置
    responsive: true, // 当 size 未指定时，根据屏幕宽度自动调整尺寸
    showTotal: (total: string | number) => `共 ${total} 条`,
    onChange: changePage,
    ...pageParams
  })

  // 请求参数
  const params = computed<reqType>(() => {
    const tempOrderParams = isRef(otherParams) ? otherParams.value || {} : otherParams || {}
    return {
      pageNo: paginationParams.value.current,
      pageSize: paginationParams.value.pageSize,
      ...tempOrderParams
    } as reqType
  })

  // 获取数据
  async function getData() {
    try {
      loading.value = true
      const response = await api(params.value)
      const { records = [], data = [], total, pages } = response.result
      const tempData = !isEmpty(records) ? records : data
      dataList.value = transformResult ? transformResult(tempData) : tempData
      paginationParams.value.total = total
      paginationParams.value.maxPages = pages
      loading.value = false
      return Promise.resolve(response)
    } catch (error) {
      console.error(error)
      loading.value = false
      return Promise.reject(error)
    }
  }

  async function refresh() {
    paginationParams.value.current = 1
    paginationParams.value.total = 0
    dataList.value = []
    await getData()
  }

  /**
   * 切换页码
   * @param pageNo 分页页码，不填默认下一页
   * @param pageSize 分页大小，不填默认当前分页大小
   */
  function changePage(pageNo?: number, pageSize?: number) {
    paginationParams.value.current = isUndefined(pageNo) ? paginationParams.value.current! + 1 : pageNo
    paginationParams.value.pageSize = isUndefined(pageSize) ? paginationParams.value.pageSize : pageSize
    getData()
  }

  onMounted(() => {
    if (immediateReqData) {
      getData()
    }
  })

  return { loading, dataList, pageParams: paginationParams, getData, refresh, changePage }
}

export default useListLoading
