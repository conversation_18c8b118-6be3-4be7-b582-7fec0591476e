/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-05-09 12:58:25
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-05-09 13:26:38
 * @FilePath: /global-intelligence-web/src/hooks/useTableHeight.ts
 * @Description:
 */
import { useWindowSize } from '@vueuse/core'
import { toNumber } from 'lodash-es'
import { nextTick, onMounted, ref, watch, type Ref } from 'vue'

/**
 * @description: 获取表格高度
 * @param {*} height 表格默认高度
 * @param {string} domPathList 需要减去的dom路径
 * @param {} offset 偏移量（表头+翻页 默认95）
 * @param {boolean} listenWindowSize 是否监听窗口大小变动
 * @return {number} 高度
 */
const useTableHeight = (
  height: any = 800,
  domPathList: string[],
  offset?: {
    tableHeight?: number
    pagination?: number
  },
  listenWindowSize: boolean = true
): Ref<number> => {
  const tableHeight = ref(height)
  const offsetObj = {
    tableHeight: 40,
    pagination: 56,
    ...offset
  }

  /**
   * @description: 获取dom元素高度
   * @param {string} domPath
   * @return {number}
   */
  function getDomHeight(domPath: string): number {
    const dom = document.querySelector<HTMLElement>(domPath)
    if (dom) {
      const domStyle = window.getComputedStyle(dom)
      const domHeight =
        dom.offsetHeight +
        toNumber(domStyle.marginTop.replace('px', '')) +
        toNumber(domStyle.marginBottom.replace('px', ''))
      return domHeight
    }
    return 0
  }

  /**
   * @description: 设置表格高度
   * @return {*}
   */
  function setTableHeight(): void {
    let domHeight = 0
    for (let index = 0; index < domPathList.length; index++) {
      const element = domPathList[index]
      domHeight += getDomHeight(element)
    }
    tableHeight.value = window.innerHeight - domHeight - (offsetObj.pagination + offsetObj.tableHeight)
  }

  // 监听窗口高度变化
  if (listenWindowSize) {
    const { height: windowHeight } = useWindowSize()
    watch(windowHeight, () => {
      setTableHeight()
    })
  }

  // 挂载后执行
  onMounted(() => {
    nextTick(() => {
      setTableHeight()
    })
  })

  return tableHeight
}

export default useTableHeight
