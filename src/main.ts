/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-02-26 14:57:11
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-10 16:46:49
 * @FilePath: /global-intelligence-web/src/main.ts
 * @Description:
 */
import { createApp } from 'vue'
import App from './App.vue'
import { setupRouter } from './router'
import { setupStore } from './store'

// antdv
// import Antd from 'ant-design-vue'
// import 'ant-design-vue/dist/reset.css'

// unocss
import 'virtual:uno.css'

// css
import './reset.css'
import './style.less'

// dayjs
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')

// 导入自定义修饰符
import { setupCommaConversion } from './utils/directive'

const app = createApp(App)
setupStore(app)
setupRouter(app)
// app.use(Antd)

// 设置自定义修饰符
setupCommaConversion(app)

app.mount('#app')
