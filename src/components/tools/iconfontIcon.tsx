/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-12 11:33:46
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-05-22 16:17:04
 * @FilePath: /global-intelligence-web/src/components/tools/iconfontIcon.tsx
 * @Description:
 */
import { createFromIconfontCN } from '@ant-design/icons-vue'
import { defineComponent } from 'vue'

// https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4865102
const IconFont = createFromIconfontCN({
  scriptUrl: '//at.alicdn.com/t/c/font_4865102_5i4eyrhhbwa.js'
})

// 如果需要修改icon的样式，查看下面链接extraCommonProps参数
// https://antdv.com/components/icon-cn#api

// 例如
// <iconfontIcon
//   icon="icon-info-circle"
//   :extra-common-props="{
//     class:[]
//     style: {
//       color: '#ccc',
//       fontSize: '16px'
//     }
//   }"
// />
export default defineComponent({
  name: 'iconfontIcon',
  props: ['icon', 'extraCommonProps'],
  components: { IconFont },
  setup(props) {
    return () => (
      <IconFont type={props.icon} class={`iconfontIcon fs-20px`} {...props.extraCommonProps} />
      // style={{ fontSize: '20px' }}
    )
  }
})
