<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-05-10 15:59:47
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-14 16:06:15
 * @FilePath: /global-intelligence-web/src/components/tools/moreIcon.vue
 * @Description: 
-->
<template>
  <div class="moreIcon transition-all">
    <a-dropdown
      :trigger="['click']"
      :overlayStyle="{
        minWidth: '90px'
      }"
      :getPopupContainer="getPopupContainer"
      placement="bottomRight"
      :arrow="{ pointAtCenter: true }"
    >
      <iconfontIcon icon="icon-ellipsis" />
      <template #overlay>
        <a-menu @click="onClick">
          <a-menu-item v-for="item in menuList" :key="item.key" :disabled="item.disabled">
            <span class="hoverPrimaryColor" :style="item.style">
              {{ item.title }}
            </span>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup lang="ts">
import iconfontIcon from '@/components/tools/iconfontIcon'
import { useVModel } from '@vueuse/core'
import type { MenuProps } from 'ant-design-vue'
import type { MenuInfo } from 'ant-design-vue/es/menu/src/interface'
import type { CSSProperties } from 'vue'

export interface menuItem {
  title: string
  key: string
  disabled?: boolean
  icon?: string
  style?: CSSProperties
}

const getPopupContainer = (_triggerNode: HTMLElement) => _triggerNode.parentNode
const props = defineProps<{ menuList: menuItem[] }>()
const menuList = useVModel(props, 'menuList')
const emits = defineEmits<{ click: [val: MenuInfo] }>()
// const visible = ref(false)

const onClick: MenuProps['onClick'] = (menuItem: MenuInfo) => {
  emits('click', menuItem)
}

// function handlerVisibleChange(_visible: boolean) {
//   visible.value = _visible
//   console.log('visible.value: ', visible.value)
// }
</script>

<style lang="less" scoped>
.moreIcon {
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  // border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  margin: 0 auto;
  border-radius: 4px;
  &:hover {
    background-color: rgba(13, 13, 13, 0.06);
  }
}
</style>
