import { message } from 'ant-design-vue'

export function timeFix() {
  const time = new Date()
  const hour = time.getHours()
  return hour < 9 ? '早上好' : hour <= 11 ? '上午好' : hour <= 13 ? '中午好' : hour < 20 ? '下午好' : '晚上好'
}

/**
 * 随机生成数字
 *
 * 示例：生成长度为 12 的随机数：randomNumber(12)
 * 示例：生成 3~23 之间的随机数：randomNumber(3, 23)
 *
 * @param1 最小值 | 长度
 * @param2 最大值
 * @return int 生成后的数字
 */
export function randomNumber(_minLength?: number, _maxLength?: number): number {
  // 生成 最小值 到 最大值 区间的随机数
  const random = (min: number, max: number) => {
    return Math.floor(Math.random() * (max - min + 1) + min)
  }
  if (arguments.length === 1) {
    const [length] = arguments
    // 生成指定长度的随机数字，首位一定不是 0
    const nums = [...Array(length).keys()].map(i => (i > 0 ? random(0, 9) : random(1, 9)))
    return parseInt(nums.join(''))
  }
  if (arguments.length >= 2) {
    const [min, max] = arguments
    return random(min, max)
  }
  return Number.NaN
}

/**
 * 随机生成字符串
 * @param length 字符串的长度
 * @param chats 可选字符串区间（只会生成传入的字符串中的字符）
 * @return string 生成的字符串
 */
export function randomString(length: number, chats: string) {
  if (!length) length = 1
  if (!chats) chats = '0123456789qwertyuioplkjhgfdsazxcvbnm'
  let str = ''
  for (let i = 0; i < length; i++) {
    const num = randomNumber(0, chats.length - 1)
    str += chats[num]
  }
  return str
}

/**
 * 随机生成uuid
 * @return string 生成的uuid
 */
export function randomUUID() {
  const chats = '0123456789abcdef'
  return randomString(32, chats)
}

/**
 * @description: 保存文件
 * @param {*} blobData 二进制文件流
 * @param {*} fileName 文件名称
 * @return {*}
 */
export function saveFile(blobData: Blob, fileName: string): void {
  if (!blobData || blobData.size === 0) {
    message.warning('文件下载失败')
    return
  }
  const url = window.URL.createObjectURL(new Blob([blobData]))
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  link.setAttribute('download', fileName)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link) // 下载完成移除元素
  window.URL.revokeObjectURL(url) // 释放掉blob对象
}

/**
 * @description: 转换企业地址显示格式
 * @param {object} companyInfo
 * @return {*}
 */
export function transformLocation(companyInfo: { province: string; city?: string; area?: string }): string {
  let { city = '', province = '' } = companyInfo
  if (province) {
    province = province
      .replace(/古自治区/g, '')
      .replace(/壮族自治区/g, '')
      .replace(/回族自治区/g, '')
      .replace(/维吾尔自治区/g, '')
      .replace(/特别行政区/g, '')
      .replace(/自治区/g, '')
      .replace(/省/g, '')
      .replace(/市/g, '')
  }

  if (city) {
    city = city
      .replace(/自治区直辖县级行政区划/g, '')
      .replace(/地区/g, '')
      .replace(/市/g, '')
      .replace(/-/g, '')
  }
  if (province) {
    province = province.replace(/-/g, '')
  }

  let text = '-'
  if ((province || city) && province !== city) {
    text = `${province}${city ? '·' : ''}${city}`
  } else if ((province || city) && province === city) {
    text = `${province}`
  } else {
    text = '-'
  }

  return text
}

/**
 * @description: 转换网址地址显示格式
 * @param {*} website
 * @return {*}
 */
export function transformWebsite(website: string): string | undefined {
  if (website) {
    let url = website
    if (!(url.indexOf('http://') > -1 || url.indexOf('https://') > -1)) {
      url = `http://${url}`
    }
    return url
  }
}
