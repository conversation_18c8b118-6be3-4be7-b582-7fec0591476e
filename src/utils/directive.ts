/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-05-30 10:20:28
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-05-30 10:56:15
 * @FilePath: /global-intelligence-web/src/utils/directive.ts
 * @Description:
 */
import type { App } from 'vue'

/**
 * 注册自定义修饰符
 * @param app Vue应用实例
 */
export function setupCommaConversion(app: App) {
  // 添加中文逗号转英文逗号的修饰符
  app.directive('encomma', {
    mounted(el, binding) {
      // 获取绑定的元素
      const inputElement = el.tagName === 'INPUT' ? el : el.querySelector('input')

      if (inputElement) {
        // 定义事件处理函数
        const handleInput = (event: Event) => {
          console.log('event: ', event)
          // 获取当前值
          const target = event.target as HTMLInputElement
          const value = target.value

          // 保存当前光标位置
          const selectionStart = target.selectionStart
          const selectionEnd = target.selectionEnd

          // 将中文逗号替换为英文逗号
          const newValue = value.replace(/，/g, ',')

          // 如果值发生了变化，更新输入框的值
          if (value !== newValue) {
            target.value = newValue

            // 恢复光标位置
            target.selectionStart = selectionStart
            target.selectionEnd = selectionEnd

            // 触发一个input事件，确保v-model能够捕获到变化
            target.dispatchEvent(new Event('input', { bubbles: true }))
          }
        }

        // 监听输入事件
        inputElement.addEventListener('input', handleInput)

        // 将事件处理函数绑定到元素上，以便在unmounted时可以移除
        inputElement._handleInput = handleInput
      }
    },
    unmounted(el, binding) {
      // 获取绑定的元素
      const inputElement = el.tagName === 'INPUT' ? el : el.querySelector('input')

      if (inputElement && inputElement._handleInput) {
        // 移除事件监听器
        inputElement.removeEventListener('input', inputElement._handleInput)
        delete inputElement._handleInput
      }
    }
  })
}
