/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-12-15 15:05:01
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-04-12 15:49:17
 * @FilePath: /corp-elf-web-consumer/src/utils/storage/local.ts
 * @Description:
 */
import { decrypto, encrypto } from '@/utils/crypto'

interface StorageData {
  value: unknown
  expire: number | null
}

/** 默认缓存期限为7天 */
const DEFAULT_CACHE_TIME = 60 * 60 * 24 * 7

/**
 * 将数据设置到本地存储中。
 * @param key 用于存储的键名。
 * @param value 要存储的数据值。
 * @param expire 数据的过期时间，单位为秒。如果不设置过期时间，可传入null，默认使用DEFAULT_CACHE_TIME。
 */
export function setLocal(key: string, value: unknown, expire: number | null = DEFAULT_CACHE_TIME) {
  // 创建存储数据对象，如果指定了过期时间，则计算其绝对时间；否则，不设置过期时间。
  const storageData: StorageData = { value, expire: expire !== null ? new Date().getTime() + expire * 1000 : null }
  // 对存储数据进行加密。
  const json = encrypto(storageData)
  // 将加密后的数据存储到localStorage中。
  window.localStorage.setItem(key, json)
}

/**
 * 从本地存储中获取数据。
 * @param key 用于在本地存储中标识数据的键名。
 * @returns 如果找到未过期的数据，则返回该数据；如果未找到或已过期，则返回 null。
 */
export function getLocal<T>(key: string) {
  // 从本地存储获取对应键名的值（JSON格式）
  const json = window.localStorage.getItem(key)
  if (json) {
    let storageData: StorageData | null = null
    try {
      // 尝试解密JSON字符串
      storageData = decrypto(json)
    } catch {} // 如果解密失败，则忽略错误，storageData保持为null

    if (storageData) {
      const { value, expire } = storageData
      // 检查数据是否未过期：若无过期时间或仍在有效期内，则直接返回数据
      if (expire === null || expire >= Date.now()) return value as T
    }
    // 如果数据已过期，则移除该本地存储数据
    removeLocal(key)
    return null
  }
  // 如果键名不存在于本地存储，返回null
  return null
}

/**
 * 从localStorage获取指定key的过期时间。
 * @param key 本地存储中的键名。
 * @returns 返回对应键名的过期时间，如果不存在或解析失败则返回null。
 */
export function getLocalExpire(key: string): number | null {
  // 尝试从localStorage获取指定键名的值
  const json = window.localStorage.getItem(key)
  if (json) {
    let storageData: StorageData | null = null
    try {
      // 尝试解密并解析存储的数据
      storageData = decrypto(json)
    } catch {} // 解析失败时忽略错误，继续流程

    if (storageData) {
      // 如果解析成功，返回存储的数据中的过期时间
      const { expire } = storageData
      return expire
    }
    // 解析成功但无过期时间数据，或解析字段缺失，返回null
    return null
  }
  // 键名不存在于localStorage，返回null
  return null
}

export function removeLocal(key: string) {
  window.localStorage.removeItem(key)
}

export function clearLocal() {
  window.localStorage.clear()
}
