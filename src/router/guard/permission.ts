/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-11 15:50:23
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-21 15:22:07
 * @FilePath: /global-intelligence-web/src/router/guard/permission.ts
 * @Description:
 */
import { NOT_FOUND_ROUTE } from '@/router/routes'
import type { Router } from 'vue-router'
import { usePermissionStore, useUserStore } from '@/store'
import { isEmpty } from 'lodash-es'
import NProgress from 'nprogress'
import { notification } from 'ant-design-vue'

// 访问白名单
const WHITE_ROUTE_LIST: string[] = ['/404', '/login']

export function createPermissionGuard(router: Router) {
  const userStore = useUserStore()
  const permissionStore = usePermissionStore()
  router.beforeEach(async (to, from, next) => {
    // console.log('from: ', from, 'to: ', to)
    NProgress.start()
    const token = userStore.getToken

    if (isEmpty(token)) {
      if (WHITE_ROUTE_LIST.includes(to.path)) {
        next()
      } else {
        next({ path: 'login', query: { ...to.query, redirect: to.path } })
      }
    } else {
      /** 有token的情况 */
      if (WHITE_ROUTE_LIST.includes(to.path)) {
        next({ path: '/', replace: true })
      } else {
        if (permissionStore.addRouters.length == 0) {
          try {
            const asyncRouter = await userStore.fetchPermissionList() // 获取用户的权限点
            asyncRouter.forEach(routerItem => router.addRoute(routerItem))
            router.addRoute(NOT_FOUND_ROUTE)

            const redirect = decodeURIComponent((from.query.redirect || to.path) as string)
            if (to.path === redirect) {
              next({ ...to, replace: true })
            } else {
              // 跳转到目的路由
              // next({ path: redirect })
              next({ path: '/' })
            }
          } catch (error: any) {
            console.error(error)
            notification.error({
              message: '提示',
              description: error.message || '获取用户信息失败！'
            })
            userStore.logout()
            next('/login')
          }
        } else {
          next()
        }
      }
    }
  })

  router.afterEach(() => {
    NProgress.done()
  })
}
