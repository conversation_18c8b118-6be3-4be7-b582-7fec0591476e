/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-10-18 17:59:32
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-13 10:37:43
 * @FilePath: /global-intelligence-web/src/router/guard/pageCache.ts
 * @Description:
 */
import { useKeepAliveCache } from './../../store/modules/keepAliveCache'
import type { Router } from 'vue-router'

export function createPageCacheGuard(router: Router) {
  const keepAliveCache = useKeepAliveCache()
  const whileList = ['/companyInfo/index', '/followCompany/list', '/dataStore/detail', '/executiveComments/detail']

  router.beforeEach((to, from) => {
    if (from.path === '/') {
      return true
    }
    console.log('from', !whileList.includes(to.path), to.path)
    if (!whileList.includes(to.path)) {
      keepAliveCache.delCachedView(from)
    }
  })
}
