/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-18 15:21:32
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-19 14:00:36
 * @FilePath: /global-intelligence-web/src/router/utils/index.ts
 * @Description:
 */
import { has, isEmpty } from 'lodash-es'
import type { RouteRecordRaw, RouteRecordRedirectOption } from 'vue-router'
import type { RouterType } from '~/types/router'

const modules = {
  ...import.meta.glob('@/views/**/**.vue'),
  ...import.meta.glob('@/layouts/**.vue')
}

function getRedirectValue(route: RouterType): RouteRecordRedirectOption | undefined {
  if (!isEmpty(route.redirect)) {
    return route.redirect as RouteRecordRedirectOption
  }
  if (!isEmpty(route.children) && Array.isArray(route.children) && route.children.length > 0) {
    return route.children[0].path
  }
  return undefined // 默认返回 undefined，避免 null 导致类型错误
}

export function convertToRouteRecord(routeList: RouterType[]): RouteRecordRaw[] {
  return [
    {
      path: '/',
      name: 'main',
      component: () => import('@/layouts/index.vue'),
      meta: { title: '首页' },
      redirect: routeList[0].path,
      children: routeList.map(convertRouteItem)
    }
  ]
}

export function convertRouteItem(route: RouterType): RouteRecordRaw {
  const componentPath = route.component.replace('@', '/src')
  const routeConfig = {
    path: route.path,
    name: route.name,
    component: modules[componentPath],
    meta: {
      title: route.meta.title || '',
      icon: route.meta?.icon || '',
      hidden: has(route.meta, 'hidden') ? route.meta.hidden : false,
      keepAlive: has(route.meta, 'keepAlive') ? route.meta.keepAlive : false,
      aggregate: has(route.meta, 'aggregate') ? route.meta.aggregate : true
    },
    redirect: getRedirectValue(route),
    children: route.children ? route.children.map(convertRouteItem) : []
  }

  return routeConfig
}
