/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-12 17:14:45
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-05-22 17:05:39
 * @FilePath: /global-intelligence-web/src/router/routes/index.ts
 * @Description: 路由配置
 */

import type { RouteRecordRaw } from 'vue-router'
import type { RouterType } from '~/types/router'

const basicRouterMap: RouteRecordRaw[] = [
  {
    name: 'login',
    path: '/login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录页', hidden: true }
  },
  { name: '404', path: '/404', component: () => import('@/views/error/404.vue'), meta: { title: '404', hidden: true } }
]

const asyncRouter: RouterType[] = [
  {
    name: 'intelligence',
    path: '/intelligence',
    redirect: '/intelligence/index',
    component: '@/layouts/keepAliveLayout.vue',
    meta: { title: '情报浏览', icon: 'icon-leidatance', keepAlive: true },
    children: [
      {
        name: 'intelligence-index',
        path: '/intelligence/index',
        component: '@/views/intelligence/index.vue',
        meta: { title: '情报浏览', keepAlive: true }
      },
      {
        name: 'topic-index',
        path: '/topic/index',
        component: '@/views/topic/index.vue',
        meta: { title: '订阅管理' }
      }
    ]
  },
  {
    name: 'industry',
    path: '/industry',
    redirect: '/industry/dashboard',
    component: '@/layouts/keepAliveLayout.vue',
    meta: { title: '行业看板', icon: 'icon-dashboard1' },
    children: [
      {
        name: 'industry-dashboard',
        path: '/industry/dashboard',
        component: '@/views/industryDashboard/index.vue',
        meta: { title: '行业看板总览' }
      }
    ]
  },
  {
    name: 'riskBoard',
    path: '/riskBoard',
    redirect: '/riskBoard/dashboard',
    component: '@/layouts/keepAliveLayout.vue',
    meta: { title: '风险看板', icon: 'icon-notification' },
    children: [
      {
        name: 'riskBoard-dashboard',
        path: '/riskBoard/dashboard',
        component: '@/views/riskBoard/index.vue',
        meta: { title: '风险看板总览' }
      }
    ]
  },
  // {
  //   name: 'economicIndicators',
  //   path: '/economicIndicators',
  //   redirect: '/economicIndicators/index',
  //   component: '@/layouts/keepAliveLayout.vue',
  //   meta: { title: '经济指标', icon: 'icon-fork', aggregate: false },
  //   children: []
  // },
  {
    name: 'economicIndicators-index',
    path: '/economicIndicators/index',
    component: '@/views/economicIndicators/economicIndicators/index.vue',
    meta: { title: '经济指标', icon: 'icon-internet' }
  },
  {
    name: 'economicIndicators-futuresMarket',
    path: '/economicIndicators/futuresMarket',
    component: '@/views/economicIndicators/futuresMarket/index.vue',
    meta: { title: '商品价格', icon: 'icon-qihuo' }
  },
  {
    name: 'economicIndicators-foreignExchange',
    path: '/economicIndicators/foreignExchange',
    component: '@/views/economicIndicators/foreignExchange/index.vue',
    meta: { title: '外汇', icon: 'icon-money-circle' }
  },
  {
    name: 'task',
    path: '/task',
    component: '@/views/task/index.vue',
    meta: { title: '外汇1', icon: 'icon-money-circle' }
  },
  {
    name: 'system',
    path: '/system',
    component: '@/layouts/keepAliveLayout.vue',
    meta: { title: '系统管理', icon: 'icon-setting', aggregate: false },
    children: [
      {
        name: 'role-index',
        path: '/role/index',
        component: '@/views/system/role/index.vue',
        meta: {
          title: '角色管理'
        }
      },
      {
        name: 'user-index',
        path: '/user/index',
        component: '@/views/system/user/index.vue',
        meta: {
          title: '用户管理'
        }
      }
    ]
  }
]

const NOT_FOUND_ROUTE = {
  name: 'NotFound',
  path: '/:pathMatch(.*)*',
  redirect: '/404',
  hidden: true
}

export { basicRouterMap, asyncRouter, NOT_FOUND_ROUTE }
