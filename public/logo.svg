<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.4.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 100 100" style="enable-background:new 0 0 100 100;" xml:space="preserve">
<style type="text/css">
	.st0{fill:url(#路径_568_00000160869843232789979130000007411698622346351256_);}
	.st1{fill:url(#形状结合_00000037655293928148666480000001423072795006029444_);}
	.st2{fill:url(#路径_567_00000021114499868998814380000000154641127790096551_);}
</style>
<g id="商瞳light" transform="translate(-816.104 -483.126)">
	<g id="组_271" transform="translate(511.229 653.081)">
		<g id="组_272" transform="translate(304.876 -169.954)">
			<g id="组_324" transform="translate(-513.017 425.38)">
				
					<linearGradient id="路径_568_00000152986930809918897980000010224852777519154621_" gradientUnits="userSpaceOnUse" x1="420.341" y1="-98.8527" x2="421.2206" y2="-98.8492" gradientTransform="matrix(113.3651 0 0 -56.8218 -47138.7188 -6017.2827)">
					<stop  offset="0" style="stop-color:#0556D8"/>
					<stop  offset="2.300000e-02" style="stop-color:#0757D9"/>
					<stop  offset="0.389" style="stop-color:#387EEF"/>
					<stop  offset="0.61" style="stop-color:#78A8F5"/>
					<stop  offset="0.933" style="stop-color:#48D7FC"/>
					<stop  offset="1" style="stop-color:#6DE1FF"/>
				</linearGradient>
				<path id="路径_568" style="fill:url(#路径_568_00000152986930809918897980000010224852777519154621_);" d="M613-375.8
					c-0.1-13.1-5.3-25.7-14.6-35c-3.9-3.9-8.5-7.2-13.5-9.6c-24.9-12.1-54.8-1.7-66.9,23.1l-4.8,9.8l50.1,0l-0.2-13.6l-26,0l0.3-0.3
					c6.8-6.7,16-10.5,25.5-10.5c20.1,0,36.4,16.3,36.4,36.4c0,0,0,0,0,0l13.6,0L613-375.8z"/>
			</g>
			
				<linearGradient id="形状结合_00000068679779059684261690000016043264659476434315_" gradientUnits="userSpaceOnUse" x1="-91.7955" y1="327.9338" x2="-92.6759" y2="327.9338" gradientTransform="matrix(113.323 0 0 -85.9445 10502.3184 28246.3301)">
				<stop  offset="0" style="stop-color:#0556D8"/>
				<stop  offset="0.389" style="stop-color:#387EEF"/>
				<stop  offset="0.678" style="stop-color:#78A8F5"/>
				<stop  offset="0.933" style="stop-color:#48D7FC"/>
				<stop  offset="1" style="stop-color:#6DE1FF"/>
			</linearGradient>
			<path id="形状结合" style="fill:url(#形状结合_00000068679779059684261690000016043264659476434315_);" d="M99.8,62.2
				L95,72c-8.4,17.1-25.9,28-45,28.1c-27.5,0-49.8-22.1-50-49.6v-0.4l13.6,0c0,20.1,16.3,36.4,36.4,36.4c0,0,0,0,0,0
				c9.6,0,18.7-3.8,25.5-10.5l0.2-0.2l-25.7,0c-6.7,0-13.2-2.6-18-7.3l-0.2-0.2c-10-10.1-9.9-26.4,0.2-36.4c0,0,0,0,0,0
				C42,22,58,21.9,68,31.6l0.2,0.2l-9.6,9.6c-4.7-4.7-12.4-4.7-17.1,0c0,0,0,0,0,0c-4.7,4.7-4.7,12.4,0,17.1
				c2.2,2.2,5.2,3.5,8.4,3.6H50l22.3,0h0.1L99.8,62.2z"/>
			<g id="组_322" transform="translate(-513.953 424.818)">
				
					<linearGradient id="路径_567_00000164491774224117765370000005953755971640809396_" gradientUnits="userSpaceOnUse" x1="423.4239" y1="-99.5018" x2="424.7612" y2="-99.5018" gradientTransform="matrix(29.1783 0 0 -43.0361 -11790.9131 -4663.7778)">
					<stop  offset="0" style="stop-color:#528FF1"/>
					<stop  offset="1" style="stop-color:#B5F0FF"/>
				</linearGradient>
				<path id="路径_567" style="fill:url(#路径_567_00000164491774224117765370000005953755971640809396_);" d="M564-362.7
					L564-362.7l22.5,0c5.4-10,3.6-22.3-4.4-30.4l-0.2-0.2c-4.8-4.7-11.2-7.3-17.9-7.3h-0.1v13.6h0.1h0.2c6.6,0.1,11.9,5.5,11.9,12.1
					C576-368.2,570.6-362.8,564-362.7z"/>
			</g>
		</g>
	</g>
</g>
</svg>
