/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-13 15:27:05
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-14 11:34:47
 * @FilePath: /global-intelligence-web/types/api/website/page.d.ts
 * @Description:
 */
import { paginationBaseRequest, paginationBaseResponse } from './../../pagination.d'

export interface websitePageReqType extends paginationBaseRequest {
  keyword?: string
  type?: string
}

export interface WebsiteVo {
  id: string
  name: string
  type: string
}

export type websitePageResType = paginationBaseResponse<WebsiteVo>
