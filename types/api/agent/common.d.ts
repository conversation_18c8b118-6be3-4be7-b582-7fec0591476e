/**
 * DifyChatBotRequest
 */
export interface ChatAgentReqType {
  /**
   * @ApiModelProperty("用户")
   * agent类型
   */
  agentType: 'GIS_AGENT_OPPORTUNITY_POINT'
  /**
   * 会话id
   */
  conversationId?: string
  /**
   * 开始id
   */
  firstId?: string
  /**
   * 默认参数
   */
  inputs?: Record<string, any>
  /**
   * 末id
   */
  lastId?: string
  /**
   * 限制条数
   */
  limit?: number
  /**
   * 查询条件
   */
  query?: string
  /**
   * 任务id
   */
  taskId?: string
  [property: string]: any
}
