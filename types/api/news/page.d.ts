import type { paginationBaseRequest } from '../../pagination'

export interface newsPageReqType extends paginationBaseRequest {
  keyword: string
  /** 行业标签 */
  industryLabelIdList: string[]
  /** 事件标签 */
  eventLabelIdList: string[]
  /** 国家标签 */
  countryLabelIdList: string[]
  /** 洲标签 */
  continentIdList: string[]
  /** 专题ID */
  topicId?: string
  /** 是否全部专题 */
  allTopic: boolean
}

export interface newsPageResType {
  /** id */
  id: number
  /** dataId */
  dataId: string
  /** 标题 */
  title: string
  /** 翻译后的标题 */
  translateTitle: string
  /** 简介 */
  summary: string
  /** 翻译后的简介 */
  translateSummary: string
  /** 内容 */
  content: string
  /** 翻译后的内容 */
  translateContent: string
  /** 作者 */
  author: string
  /** url */
  detailUrl: string
  /** 站点 */
  host: string
  /** 发布时间 */
  publishTime: string
  /** 标签列表 */
  tagMap: Record<string, string[]>
  /** 是否阅读过 */
  isRead: boolean
  tagList: { label: string; type: string }[]
  /** 是否中文 */
  isZh: boolean
  /** 站点名称 */
  websiteName: string
  /** 新闻所属专题id */
  topicIds: string[] | null
}
