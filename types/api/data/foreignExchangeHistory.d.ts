/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-04-27 10:22:17
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-04-27 16:11:36
 * @FilePath: /global-intelligence-web/types/api/data/foreignExchangeHistory.d.ts
 * @Description:
 */
import { paginationBaseRequest } from '../../pagination'

export interface foreignExchangeHistoryReqType {
  /**
   * 货币对code
   */
  currencyPairCode: string
  /**
   * 开始时间
   */
  beginTime: string
  /**
   * 结束时间
   */
  endTime: string
}
export interface foreignExchangeHistoryResType {
  createTime?: string
  /**
   * 货币对
   */
  currencyPair?: string
  /**
   * 货币对code
   */
  currencyPairCode?: string
  id?: number
  isDeleted?: number
  /**
   * 中间价
   */
  medianPrice?: number
  /**
   * 发布日期
   */
  publishTime?: string
  updateTime?: string
  /**
   * 涨跌
   */
  upDown?: number
  [property: string]: any
}
