/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-04-27 10:22:17
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-04-27 10:32:55
 * @FilePath: /global-intelligence-web/types/api/data/futuresMarketPage.d.ts
 * @Description:
 */

import type { paginationBaseRequest } from '~/types/pagination'

export interface futuresMarketPageReqType extends paginationBaseRequest {
  /** 交易所 */
  exchange?: string
  /** 商品 */
  goods?: string
  /** 名称 */
  name?: string
}
export interface futuresMarketPageResType {
  /**
   * 今开
   */
  beginPrice?: string
  /**
   * 代码
   */
  code?: string
  createTime?: string
  /**
   * 交易所
   */
  exchange?: string
  /**
   * 最高
   */
  highestPrice?: string
  id?: number
  /**
   * 卖盘（内盘）
   */
  insideDisc?: string
  isDeleted?: number
  /**
   * 最新价
   */
  lastPrice?: string
  /**
   * 最低
   */
  lowestPrice?: string
  /**
   * 名称
   */
  name?: string
  /**
   * 持仓量
   */
  openInterest?: string
  /**
   * 买盘（外盘）
   */
  outerDisc?: string
  /**
   * 涨跌额
   */
  riseFallAmount?: string
  /**
   * 涨跌幅
   */
  riseFallRate?: string
  /**
   * 成交额
   */
  transactionVolume?: string
  /**
   * 成交量
   */
  turnover?: string
  updateTime?: string
  /**
   * 昨结
   */
  yesterdayEndPrice?: string
  [property: string]: any
}
