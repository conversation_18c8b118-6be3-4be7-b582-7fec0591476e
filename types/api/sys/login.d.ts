/**
 * SysLoginVo
 */
export interface SysLoginReqType {
  /**
   * 密码
   */
  password: string
  /**
   * 账号
   */
  username: string
}

/**
 * LoginVo
 */
export interface SysLoginResType {
  /**
   * token
   */
  token: string
  /**
   * userVo
   */
  userVo: SysUserLoginVo
}

/**
 * SysUserLoginVo
 */
export interface SysUserLoginVo {
  /**
   * 邮箱
   */
  email?: string
  /**
   * 所属部门id
   */
  groupId?: string
  id: string
  /**
   * 电话
   */
  mobile?: string
  /**
   * 真实姓名
   */
  realName?: string
  /**
   * 角色列表
   */
  roles: string[]
  /**
   * 用户名称
   */
  username: string
}
