/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-13 15:39:12
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-04-03 10:28:32
 * @FilePath: /global-intelligence-web/types/api/newsTopic/common.d.ts
 * @Description:
 */
export interface NewsTopicType {
  id?: string
  /** 专题名称 */
  topicName: string
  /** 站点id */
  websiteIds: string[]
  /** 数据标签 */
  tagConfig: MapListSystemLabelVo
  /** 品牌标签 */
  // brandList: string[]
  /** 订阅状态 0关闭 1监控中 */
  monitorState: number
  /** 推送数量（近30日） */
  lastThirtyTotal: number
}

export type CreatedNewsTopicType = Omit<NewsTopicType, 'id' | 'monitorState' | 'lastThirtyTotal' | 'brandList'>
export interface UpdateNewsTopicType
  extends Required<Omit<NewsTopicType, 'monitorState' | 'lastThirtyTotal' | 'brandList'>> {
  monitorState?: number
}

/**
 * Map«List«SystemLabelVo»»
 */
export type MapListSystemLabelVo = Record<string, SystemLabelVo[]>

// /**
//  * key
//  */
// export interface Key {
//   id?: null | string
//   labelName?: null | string
//   subList?: SystemLabelVo[] | null
//
// }

/**
 * com.datastory.b2b.intelligence.modules.systemLabel.entity.vo.SystemLabelVo
 *
 * SystemLabelVo
 */
export interface SystemLabelVo {
  id: string
  labelName: string
  subList?: SystemLabelVo[]
}
