/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-13 15:28:26
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-14 10:50:34
 * @FilePath: /global-intelligence-web/types/api/newsTopic/page.d.ts
 * @Description:
 */
import { paginationBaseRequest } from './../../pagination.d'
import type { NewsTopicType } from './common'

export interface NewsTopicPageReqType extends paginationBaseRequest {
  /* 关键字 */
  keyword?: string
  /* 监控状态 0关闭 1监控中 */
  monitorState?: number
  
}

export type NewsTopicPageResType = paginationBaseResponse<NewsTopicType>
