/**
 * CreateUserVo
 */
export interface UserType {
  /**
   * 邮箱
   */
  email?: string
  /**
   * id
   */
  id?: string
  /**
   * 电话
   */
  phone?: string
  /**
   * 密码
   */
  password?: string
  /**
   * 用户名称
   */
  userName: string
  /**
   * 角色
   */
  roleIds: string[]
}

export type CreatedUserType = Omit<UserType, 'id'>
export type UpdateUserType = Pick<UserType, 'id' | 'userName' | 'roleIds'>
