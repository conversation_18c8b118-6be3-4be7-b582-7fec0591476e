/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-18 10:51:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-19 15:21:13
 * @FilePath: /global-intelligence-web/types/api/user/page.d.ts
 * @Description:
 */
import type { RoleType } from '../role/common'
import { paginationBaseRequest, paginationBaseResponse } from './../../pagination.d'
import { UserType } from './common'

export interface UserPageReqType extends paginationBaseRequest {
  /**
   * 关键字
   */
  keyword?: string
  /**
   * 页码（最低1页）
   */
  pageNo?: number
  /**
   * 页数（最高1000条）
   */
  pageSize?: number
  /**
   * 角色ID
   */
  roleIds?: string
}

export interface UserPageType {
  id: string
  roleVos: Required<RoleType>[]
  // roleNames: string[]
  // roleIds: string[]
  state: 'ENABLE' | 'DISABLE'
  userName: string
  /**
   * 邮箱
   */
  email: string
}
export type UserPageResType = paginationBaseResponse<UserPageType>
