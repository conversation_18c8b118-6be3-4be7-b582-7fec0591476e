/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-18 10:51:43
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-19 15:20:35
 * @FilePath: /global-intelligence-web/types/api/role/common.d.ts
 * @Description:
 */
/**
 * RoleVo
 */
export interface RoleType {
  /**
   * id
   */
  id?: string
  /**
   * 角色拥有的权限点
   */
  permissionIds: string[]
  /**
   * 角色名称
   */
  roleName: string
  /**
   * 有多少人用了这个角色
   */
  userNum: number
}

export type CreatedRoleType = Omit<RoleType, 'id' | 'userNum'>
export type UpdateRoleType = Omit<RoleType, 'userNum'>
