/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-03-19 11:29:52
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-19 11:32:02
 * @FilePath: /global-intelligence-web/types/api/role/permissionList.d.ts
 * @Description:
 */
export interface RolePermissionType {
  id: string
  parentId: string
  permissionName: string
  subList: RolePermissionType[]
}

export type RolePermissionResType = RolePermissionType[]
