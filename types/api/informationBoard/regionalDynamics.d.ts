import { NewsItemType } from './common.d';
/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-05-07 17:32:09
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-05-08 11:36:30
 * @FilePath: /global-intelligence-web/types/api/informationBoard/regionalDynamics.d.ts
 * @Description: 
 */
export interface regionalDynamicsReqType {
  /**
   * 行业id
   */
  industryId?: string
  /**
   * 分页大小
   */
  size?: number
  [property: string]: any
}
export interface regionalDynamicsResType {
  /** 洲 */
  continent?: string
  /**
   * 动态内容
   */
  newsList?: NewsItemType[]
}
