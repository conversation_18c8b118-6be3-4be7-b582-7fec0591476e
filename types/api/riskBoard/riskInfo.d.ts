export interface riskInfoResType {
  /**
   * 告警开始时间
   */
  alarmBeginTime?: string
  /**
   * 告警结束时间
   */
  alarmEndTime?: string
  /**
   * 国家
   */
  country?: string
  id?: number
  /**
   * 影响等级 1 关键 2 重要 3 有限
   */
  impactLevel?: number
  /**
   * 纬度
   */
  latitude?: string
  /**
   * 等级 1 高 2 中 3 低
   */
  level?: number
  /**
   * 经度
   */
  longitude?: string
  /**
   * 风险名称
   */
  riskName?: string
  /**
   * 风险子类型
   */
  riskSubType?: string
  /**
   * 风险类型
   */
  riskType?: string
  /**
   * 状态  1 恶化 2 不变  3 改善 4 结束
   */
  status?: number
}
