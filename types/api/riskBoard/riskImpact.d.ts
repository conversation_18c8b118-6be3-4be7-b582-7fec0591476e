/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-05-19 11:13:34
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-05-19 11:27:17
 * @FilePath: /global-intelligence-web/types/api/riskBoard/riskImpact.d.ts
 * @Description: 
 */
import { paginationBaseRequest } from './../../pagination.d'
export interface riskImpactReqType extends paginationBaseRequest {
  /** 影响类型 */
  impactType?: string
}
export interface riskImpactResType {
  /**
   * 内容
   */
  content?: string
  id?: number
  /**
   * 影响类型
   */
  impactType?: string
  /**
   * 程度 1 关键 2 重要 3 有限
   */
  level?: number
  /**
   * 原因
   */
  reason?: string
}
