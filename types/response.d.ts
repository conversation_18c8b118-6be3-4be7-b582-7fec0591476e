/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-11 14:26:50
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-02-06 17:49:39
 * @FilePath: /corp-elf-web-consumer/types/response.d.ts
 * @Description: 请求or响应类型
 */
import type { AxiosError } from 'axios'

// 正确响应类型
export interface ResponseData<T = any> {
  code: number | string
  message: string
  msg?: string
  result: T
  success: boolean
  timestamp: number
}
// 错误响应类型
export interface ErrorMessageType {
  config: AxiosError
  response: ResponseData | undefined
  message: string | undefined
  requestUrl: string
}
