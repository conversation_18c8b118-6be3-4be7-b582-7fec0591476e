/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-02-04 11:24:04
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-09-12 11:09:14
 * @FilePath: /corp-elf-web-consumer/types/common/pagination.ts
 * @Description:
 */
export interface paginationBaseRequest {
  pageNo?: number
  pageSize?: number
}

export interface paginationBaseResponse<T> {
  total: number // 总数
  size: number // 分页大小
  current: number // 当前页数
  pages: number // 总共多少页
  records: Array<T>
  data?: Array<T>
}
