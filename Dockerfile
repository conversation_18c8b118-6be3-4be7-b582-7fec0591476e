FROM node:20-alpine AS build-stage
WORKDIR /app
COPY package.json .npmrc ./
RUN npm install --registry=https://registry.npmmirror.com
COPY . .
ARG STAGE=dev
RUN npm run build:${STAGE} 

FROM nginx:stable-alpine AS production-stage
# 设置时区和OS默认字符编码，避免时间戳转换时默认字符格式不对
ENV LANG=en_US.UTF-8
ENV LANGUAGE en_US:en
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
# 拷贝文件到nginx容器
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY ./nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
