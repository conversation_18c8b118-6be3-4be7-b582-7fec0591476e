/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-12-15 14:59:10
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-14 11:49:32
 * @FilePath: /global-intelligence-web/uno.config.ts
 * @Description:
 */
// uno.config.ts
import { defineConfig, presetUno, transformerVariantGroup, transformerDirectives } from 'unocss'
import { presetScalpel } from 'unocss-preset-scalpel'
import { presetScrollbar } from 'unocss-preset-scrollbar'

export default defineConfig({
  presets: [
    presetUno(),
    presetScalpel(),
    presetScrollbar()
    // ...自定义预设
  ],
  transformers: [transformerVariantGroup(), transformerDirectives()],
  rules: [['box-shadow', { 'box-shadow': '0px 2px 6px 0px rgba(0,0,0, 0.04), 0px 4px 12px 0px rgba(0,0,0, 0.02)' }]]
})
